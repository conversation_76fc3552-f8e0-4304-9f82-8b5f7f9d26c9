{"ast": null, "code": "// Game Redux slice\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport apiClient from '../../../shared/services/api/client';\nimport gameApi from '../../../shared/services/game/gameApi';\n\n// Initial state\nconst initialState = {\n  // Current game status\n  currentRound: \"1\",\n  currentTestName: \"\",\n  isActive: false,\n  isHost: false,\n  // Questions and answers\n  currentQuestion: null,\n  selectedPacketName: \"\",\n  questions: [],\n  currentCorrectAnswer: \"\",\n  // Players and scoring\n  players: [],\n  currentPlayer: null,\n  scores: [],\n  scoreRules: null,\n  // Round-specific data\n  round2Grid: null,\n  round4Grid: null,\n  // Game settings\n  mode: 'manual',\n  timeLimit: 30,\n  // UI state\n  showRules: false,\n  currentTurn: 0,\n  currentQuestionNumber: 1,\n  isBuzzOpen: false,\n  // Loading states\n  loading: {\n    isLoading: false,\n    error: null\n  },\n  // joing states\n  joining: {\n    isLoading: false,\n    error: null\n  },\n  //input disabled\n  isInputDisabled: true\n};\nexport const joinRoom = createAsyncThunk('room/joinRoom', async (joinData, {\n  rejectWithValue\n}) => {\n  try {\n    const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);\n    url.searchParams.append('room_id', joinData.roomId);\n    if (joinData.password) {\n      url.searchParams.append('password', joinData.password);\n    }\n    const response = await apiClient.post(url.toString(), joinData, {\n      _isAuthRequired: true\n    });\n    return response.data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n// Async thunks\nexport const getQuestions = createAsyncThunk('game/fetchQuestions', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    const question = await gameApi.getQuestions(params);\n    return question;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const submitAnswer = createAsyncThunk('game/submitAnswer', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/answer', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to submit answer');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const updateScores = createAsyncThunk('game/updateScores', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/scoring', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to update scores');\n    }\n    const data = await response.json();\n    return data.scores;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n\n// Game slice\nconst gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Game state management\n    setCurrentRound: (state, action) => {\n      state.currentRound = action.payload;\n      state.currentQuestionNumber = 1; // Reset question number when round changes\n    },\n    setCurrentTestName: (state, action) => {\n      state.currentTestName = action.payload;\n    },\n    setSelectedPacketName: (state, action) => {\n      state.selectedPacketName = action.payload;\n    },\n    setIsInputDisabled: (state, action) => {\n      state.isInputDisabled = action.payload;\n    },\n    setIsActive: (state, action) => {\n      state.isActive = action.payload;\n    },\n    setIsHost: (state, action) => {\n      state.isHost = action.payload;\n    },\n    // Question management\n    setCurrentQuestion: (state, action) => {\n      state.currentQuestion = action.payload;\n    },\n    setQuestions: (state, action) => {\n      state.questions = action.payload;\n    },\n    setCurrentCorrectAnswer: (state, action) => {\n      state.currentCorrectAnswer = action.payload;\n    },\n    setPlayerAnswerList: (state, action) => {\n      state.players = state.players.map(player => {\n        const answerUpdate = action.payload.find(a => a.uid === player.uid);\n        return answerUpdate ? {\n          ...player,\n          ...answerUpdate\n        } : player;\n      });\n    },\n    clearPlayerAnswerList: state => {\n      state.players = state.players.map(player => ({\n        ...player,\n        answer: \"\",\n        time: 0\n      }));\n    },\n    nextQuestion: state => {\n      state.currentQuestionNumber += 1;\n    },\n    setCurrentQuestionNumber: (state, action) => {\n      state.currentQuestionNumber = action.payload;\n    },\n    // Player management\n    setPlayers: (state, action) => {\n      state.players = state.players.map(player => {\n        const update = action.payload.find(p => p && p.uid === player.uid);\n        return update ? {\n          ...player,\n          ...update\n        } : player;\n      });\n    },\n    setCurrentPlayer: (state, action) => {\n      state.currentPlayer = action.payload;\n    },\n    updatePlayer: (state, action) => {\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (playerIndex !== -1) {\n        state.players[playerIndex] = {\n          ...state.players[playerIndex],\n          ...action.payload.updates\n        };\n      }\n    },\n    addPlayer: (state, action) => {\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (existingIndex === -1) {\n        state.players.push(action.payload);\n      }\n    },\n    removePlayer: (state, action) => {\n      state.players = state.players.filter(p => p.uid !== action.payload);\n    },\n    setPlayerAnswer: (state, action) => {\n      if (state.currentPlayer) {\n        state.currentPlayer.answer = action.payload.answer;\n        state.currentPlayer.time = action.payload.time;\n      }\n    },\n    // Scoring\n    setScores: (state, action) => {\n      state.scores = action.payload;\n    },\n    setScoreRules: (state, action) => {\n      state.scoreRules = action.payload;\n    },\n    // Round-specific data\n    setRound2Grid: (state, action) => {\n      if (action.payload === null) {\n        state.round2Grid = null;\n      } else {\n        state.round2Grid = {\n          ...(state.round2Grid || {}),\n          ...action.payload\n        };\n      }\n    },\n    setRound4Grid: (state, action) => {\n      if (action.payload === null) {\n        state.round4Grid = null;\n      } else {\n        state.round4Grid = {\n          ...(state.round4Grid || {}),\n          ...action.payload\n        };\n      }\n    },\n    // Game settings\n    setMode: (state, action) => {\n      state.mode = action.payload;\n    },\n    setTimeLimit: (state, action) => {\n      state.timeLimit = action.payload;\n    },\n    // UI state\n    setShowRules: (state, action) => {\n      state.showRules = action.payload;\n    },\n    setCurrentTurn: (state, action) => {\n      state.currentTurn = action.payload;\n    },\n    setIsBuzzOpen: (state, action) => {\n      state.isBuzzOpen = action.payload;\n    },\n    // Reset game state\n    resetGame: state => {\n      return {\n        ...initialState,\n        isHost: state.isHost\n      };\n    },\n    // Error handling\n    clearError: state => {\n      state.loading.error = null;\n    }\n  },\n  extraReducers: builder => {\n    // Join room\n    builder.addCase(joinRoom.pending, state => {\n      state.joining.isLoading = true;\n      state.joining.error = null;\n    }).addCase(joinRoom.fulfilled, (state, action) => {\n      state.joining.isLoading = false;\n      state.players = action.payload.players;\n      state.currentPlayer = {\n        ...state.currentPlayer,\n        ...action.meta.arg,\n        uid: action.payload.uid\n      };\n      state.isHost = false;\n    }).addCase(joinRoom.rejected, (state, action) => {\n      state.joining.isLoading = false;\n      state.joining.error = action.payload;\n    });\n    // Fetch questions\n    builder.addCase(getQuestions.pending, state => {\n      state.loading.isLoading = true;\n      state.loading.error = null;\n    }).addCase(getQuestions.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      state.currentQuestion = action.payload;\n      state.currentCorrectAnswer = action.payload.answer;\n    }).addCase(getQuestions.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Submit answer\n    builder.addCase(submitAnswer.pending, state => {\n      state.loading.isLoading = true;\n    }).addCase(submitAnswer.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      // Handle answer submission result\n    }).addCase(submitAnswer.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Update scores\n    builder.addCase(updateScores.fulfilled, (state, action) => {\n      state.scores = action.payload;\n    }).addCase(updateScores.rejected, (state, action) => {\n      state.loading.error = action.payload;\n    });\n  }\n});\nexport const {\n  setCurrentRound,\n  setIsActive,\n  setIsHost,\n  setCurrentQuestion,\n  setQuestions,\n  setCurrentCorrectAnswer,\n  nextQuestion,\n  setCurrentQuestionNumber,\n  setPlayers,\n  setCurrentPlayer,\n  setPlayerAnswer,\n  setPlayerAnswerList,\n  clearPlayerAnswerList,\n  updatePlayer,\n  addPlayer,\n  removePlayer,\n  setScores,\n  setScoreRules,\n  setRound2Grid,\n  setRound4Grid,\n  setMode,\n  setTimeLimit,\n  setShowRules,\n  setCurrentTurn,\n  resetGame,\n  clearError,\n  setIsInputDisabled,\n  setIsBuzzOpen\n} = gameSlice.actions;\nexport default gameSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "apiClient", "gameApi", "initialState", "currentRound", "currentTestName", "isActive", "isHost", "currentQuestion", "selectedPacketName", "questions", "currentCorrectAnswer", "players", "currentPlayer", "scores", "scoreRules", "round2Grid", "round4Grid", "mode", "timeLimit", "showRules", "currentTurn", "currentQuestionNumber", "isBuzzOpen", "loading", "isLoading", "error", "joining", "isInputDisabled", "joinRoom", "joinData", "rejectWithValue", "url", "URL", "process", "env", "REACT_APP_BASE_URL", "searchParams", "append", "roomId", "password", "response", "post", "toString", "_isAuthRequired", "data", "message", "getQuestions", "params", "question", "submitAnswer", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "json", "updateScores", "gameSlice", "name", "reducers", "setCurrentRound", "state", "action", "payload", "setCurrentTestName", "setSelectedPacketName", "setIsInputDisabled", "setIsActive", "setIsHost", "setCurrentQuestion", "setQuestions", "setCurrentCorrectAnswer", "setPlayerAnswerList", "map", "player", "answerUpdate", "find", "a", "uid", "clearPlayerAnswerList", "answer", "time", "nextQuestion", "setCurrentQuestionNumber", "setPlayers", "update", "p", "setCurrentPlayer", "updatePlayer", "playerIndex", "findIndex", "updates", "addPlayer", "existingIndex", "push", "removePlayer", "filter", "setPlayerAnswer", "setScores", "setScoreRules", "setRound2Grid", "setRound4Grid", "setMode", "setTimeLimit", "setShowRules", "setCurrentTurn", "setIsBuzzOpen", "resetGame", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "meta", "arg", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/gameSlice.ts"], "sourcesContent": ["// Game Redux slice\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { GameState, Question, Score, PlayerData, Round2Grid, Round4Grid, ScoreRule, RoomPlayer, JoinRoomRequest, Answer, GetQuestionsRequest } from '../../../shared/types';\r\nimport apiClient from '../../../shared/services/api/client';\r\nimport { set } from 'firebase/database';\r\nimport gameApi from '../../../shared/services/game/gameApi';\r\n\r\n// Initial state\r\nconst initialState: GameState = {\r\n  // Current game status\r\n  currentRound: \"1\",\r\n  currentTestName: \"\",\r\n  isActive: false,\r\n  isHost: false,\r\n\r\n  // Questions and answers\r\n  currentQuestion: null,\r\n  selectedPacketName: \"\",\r\n  questions: [],\r\n  currentCorrectAnswer: \"\",\r\n  // Players and scoring\r\n  players: [],\r\n  currentPlayer: null,\r\n  scores: [],\r\n  scoreRules: null,\r\n\r\n  // Round-specific data\r\n  round2Grid: null,\r\n  round4Grid: null,\r\n\r\n  // Game settings\r\n  mode: 'manual',\r\n  timeLimit: 30,\r\n\r\n  // UI state\r\n  showRules: false,\r\n  currentTurn: 0,\r\n  currentQuestionNumber: 1,\r\n  isBuzzOpen: false,\r\n\r\n  // Loading states\r\n  loading: {\r\n    isLoading: false,\r\n    error: null,\r\n  },\r\n\r\n  // joing states\r\n  joining: {\r\n    isLoading: false,\r\n    error: null,\r\n  },\r\n\r\n  //input disabled\r\n  isInputDisabled: true,\r\n};\r\n\r\n\r\nexport const joinRoom = createAsyncThunk(\r\n  'room/joinRoom',\r\n  async (joinData: JoinRoomRequest, { rejectWithValue }) => {\r\n    try {\r\n      const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);\r\n      url.searchParams.append('room_id', joinData.roomId);\r\n      if (joinData.password) {\r\n        url.searchParams.append('password', joinData.password);\r\n      }\r\n      const response = await apiClient.post(url.toString(), joinData, { _isAuthRequired: true } as any);\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n// Async thunks\r\nexport const getQuestions = createAsyncThunk(\r\n  'game/fetchQuestions',\r\n  async (params: GetQuestionsRequest, { rejectWithValue }) => {\r\n    try {\r\n      const question = await gameApi.getQuestions(params);\r\n\r\n      return question;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const submitAnswer = createAsyncThunk(\r\n  'game/submitAnswer',\r\n  async (params: { roomId: string; uid: string; answer: string; time: number }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/game/answer', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to submit answer');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const updateScores = createAsyncThunk(\r\n  'game/updateScores',\r\n  async (params: { roomId: string; mode: string; scores?: Score[]; round: string }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/game/scoring', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to update scores');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data.scores;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\n// Game slice\r\nconst gameSlice = createSlice({\r\n  name: 'game',\r\n  initialState,\r\n  reducers: {\r\n    // Game state management\r\n    setCurrentRound: (state, action: PayloadAction<string>) => {\r\n      state.currentRound = action.payload;\r\n      state.currentQuestionNumber = 1; // Reset question number when round changes\r\n    },\r\n\r\n    setCurrentTestName: (state, action: PayloadAction<string>) => {\r\n      state.currentTestName = action.payload;\r\n    },\r\n\r\n    setSelectedPacketName: (state, action: PayloadAction<string>) => {\r\n      state.selectedPacketName = action.payload;\r\n    },\r\n\r\n    setIsInputDisabled: (state, action: PayloadAction<boolean>) => {\r\n      state.isInputDisabled = action.payload;\r\n    },\r\n\r\n    setIsActive: (state, action: PayloadAction<boolean>) => {\r\n      state.isActive = action.payload;\r\n    },\r\n\r\n    setIsHost: (state, action: PayloadAction<boolean>) => {\r\n      state.isHost = action.payload;\r\n    },\r\n\r\n    // Question management\r\n    setCurrentQuestion: (state, action: PayloadAction<Question | null>) => {\r\n      state.currentQuestion = action.payload;\r\n    },\r\n\r\n    setQuestions: (state, action: PayloadAction<Question[]>) => {\r\n      state.questions = action.payload;\r\n    },\r\n\r\n    setCurrentCorrectAnswer: (state, action: PayloadAction<string>) => {\r\n      state.currentCorrectAnswer = action.payload;\r\n    },\r\n\r\n    setPlayerAnswerList: (state, action: PayloadAction<Answer[]>) => {\r\n      state.players = state.players.map(player => {\r\n        const answerUpdate = action.payload.find(a => a.uid === player.uid);\r\n        return answerUpdate ? { ...player, ...answerUpdate } : player;\r\n      });\r\n    },\r\n\r\n    clearPlayerAnswerList: (state) => {\r\n      state.players = state.players.map(player => ({\r\n        ...player,\r\n        answer: \"\",\r\n        time: 0,\r\n      }));\r\n    },\r\n\r\n    nextQuestion: (state) => {\r\n      state.currentQuestionNumber += 1;\r\n    },\r\n\r\n    setCurrentQuestionNumber: (state, action: PayloadAction<number>) => {\r\n      state.currentQuestionNumber = action.payload;\r\n    },\r\n    // Player management\r\n    setPlayers: (state, action: PayloadAction<Partial<PlayerData[]>>) => {\r\n      state.players = state.players.map(player => {\r\n        const update = action.payload.find(p => p && p.uid === player.uid);\r\n        return update ? { ...player, ...update } : player;\r\n      });\r\n    },\r\n\r\n    setCurrentPlayer: (state, action: PayloadAction<RoomPlayer>) => {\r\n      state.currentPlayer = action.payload;\r\n    },\r\n\r\n    updatePlayer: (state, action: PayloadAction<{ uid: string; updates: Partial<PlayerData> }>) => {\r\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\r\n      if (playerIndex !== -1) {\r\n        state.players[playerIndex] = { ...state.players[playerIndex], ...action.payload.updates };\r\n      }\r\n    },\r\n\r\n    addPlayer: (state, action: PayloadAction<PlayerData>) => {\r\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\r\n      if (existingIndex === -1) {\r\n        state.players.push(action.payload);\r\n      }\r\n    },\r\n\r\n    removePlayer: (state, action: PayloadAction<string>) => {\r\n      state.players = state.players.filter(p => p.uid !== action.payload);\r\n    },\r\n\r\n    setPlayerAnswer: (state, action: PayloadAction<{ answer: string; time: number }>) => {\r\n      if (state.currentPlayer) {\r\n        state.currentPlayer.answer = action.payload.answer;\r\n        state.currentPlayer.time = action.payload.time;\r\n      }\r\n    },\r\n\r\n    // Scoring\r\n    setScores: (state, action: PayloadAction<Score[]>) => {\r\n      state.scores = action.payload;\r\n    },\r\n\r\n    setScoreRules: (state, action: PayloadAction<ScoreRule>) => {\r\n      state.scoreRules = action.payload;\r\n    },\r\n\r\n    // Round-specific data\r\n    setRound2Grid: (state, action: PayloadAction<Partial<Round2Grid | null>>) => {\r\n      if (action.payload === null) {\r\n        state.round2Grid = null;\r\n      } else {\r\n        state.round2Grid = {\r\n          ...(state.round2Grid || {}),\r\n          ...action.payload,\r\n        };\r\n      }\r\n    },\r\n\r\n    setRound4Grid: (state, action: PayloadAction<Partial<Round4Grid | null>>) => {\r\n      if (action.payload === null) {\r\n        state.round4Grid = null;\r\n      } else {\r\n        state.round4Grid = {\r\n          ...(state.round4Grid || {}),\r\n          ...action.payload,\r\n        };\r\n      }\r\n    },\r\n\r\n    // Game settings\r\n    setMode: (state, action: PayloadAction<'manual' | 'auto' | 'adaptive'>) => {\r\n      state.mode = action.payload;\r\n    },\r\n\r\n    setTimeLimit: (state, action: PayloadAction<number>) => {\r\n      state.timeLimit = action.payload;\r\n    },\r\n\r\n    // UI state\r\n    setShowRules: (state, action: PayloadAction<boolean>) => {\r\n      state.showRules = action.payload;\r\n    },\r\n\r\n    setCurrentTurn: (state, action: PayloadAction<number>) => {\r\n      state.currentTurn = action.payload;\r\n    },\r\n\r\n    setIsBuzzOpen: (state, action: PayloadAction<boolean>) => {\r\n      state.isBuzzOpen = action.payload;\r\n    },\r\n\r\n    // Reset game state\r\n    resetGame: (state) => {\r\n      return { ...initialState, isHost: state.isHost };\r\n    },\r\n\r\n    // Error handling\r\n    clearError: (state) => {\r\n      state.loading.error = null;\r\n    },\r\n  },\r\n\r\n  extraReducers: (builder) => {\r\n    // Join room\r\n    builder\r\n      .addCase(joinRoom.pending, (state) => {\r\n        state.joining.isLoading = true;\r\n        state.joining.error = null;\r\n      })\r\n      .addCase(joinRoom.fulfilled, (state, action) => {\r\n        state.joining.isLoading = false;\r\n        state.players = action.payload.players;\r\n        state.currentPlayer = {\r\n          ...state.currentPlayer,\r\n          ...action.meta.arg,\r\n          uid: action.payload.uid\r\n        }\r\n        state.isHost = false;\r\n      })\r\n      .addCase(joinRoom.rejected, (state, action) => {\r\n        state.joining.isLoading = false;\r\n        state.joining.error = action.payload as string;\r\n      });\r\n    // Fetch questions\r\n    builder\r\n      .addCase(getQuestions.pending, (state) => {\r\n        state.loading.isLoading = true;\r\n        state.loading.error = null;\r\n      })\r\n      .addCase(getQuestions.fulfilled, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.currentQuestion = action.payload;\r\n        state.currentCorrectAnswer = action.payload.answer\r\n      })\r\n      .addCase(getQuestions.rejected, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.loading.error = action.payload as string;\r\n      });\r\n\r\n    // Submit answer\r\n    builder\r\n      .addCase(submitAnswer.pending, (state) => {\r\n        state.loading.isLoading = true;\r\n      })\r\n      .addCase(submitAnswer.fulfilled, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        // Handle answer submission result\r\n      })\r\n      .addCase(submitAnswer.rejected, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.loading.error = action.payload as string;\r\n      });\r\n\r\n    // Update scores\r\n    builder\r\n      .addCase(updateScores.fulfilled, (state, action) => {\r\n        state.scores = action.payload;\r\n      })\r\n      .addCase(updateScores.rejected, (state, action) => {\r\n        state.loading.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const {\r\n  setCurrentRound,\r\n  setIsActive,\r\n  setIsHost,\r\n  setCurrentQuestion,\r\n  setQuestions,\r\n  setCurrentCorrectAnswer,\r\n  nextQuestion,\r\n  setCurrentQuestionNumber,\r\n  setPlayers,\r\n  setCurrentPlayer,\r\n  setPlayerAnswer,\r\n  setPlayerAnswerList,\r\n  clearPlayerAnswerList,\r\n  updatePlayer,\r\n  addPlayer,\r\n  removePlayer,\r\n  setScores,\r\n  setScoreRules,\r\n  setRound2Grid,\r\n  setRound4Grid,\r\n  setMode,\r\n  setTimeLimit,\r\n  setShowRules,\r\n  setCurrentTurn,\r\n  resetGame,\r\n  clearError,\r\n  setIsInputDisabled,\r\n  setIsBuzzOpen\r\n} = gameSlice.actions;\r\n\r\nexport default gameSlice.reducer;\r\n"], "mappings": "AAAA;AACA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAE/E,OAAOC,SAAS,MAAM,qCAAqC;AAE3D,OAAOC,OAAO,MAAM,uCAAuC;;AAE3D;AACA,MAAMC,YAAuB,GAAG;EAC9B;EACAC,YAAY,EAAE,GAAG;EACjBC,eAAe,EAAE,EAAE;EACnBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,KAAK;EAEb;EACAC,eAAe,EAAE,IAAI;EACrBC,kBAAkB,EAAE,EAAE;EACtBC,SAAS,EAAE,EAAE;EACbC,oBAAoB,EAAE,EAAE;EACxB;EACAC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,EAAE;EACVC,UAAU,EAAE,IAAI;EAEhB;EACAC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAEhB;EACAC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,EAAE;EAEb;EACAC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,CAAC;EACdC,qBAAqB,EAAE,CAAC;EACxBC,UAAU,EAAE,KAAK;EAEjB;EACAC,OAAO,EAAE;IACPC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,OAAO,EAAE;IACPF,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAE,eAAe,EAAE;AACnB,CAAC;AAGD,OAAO,MAAMC,QAAQ,GAAG7B,gBAAgB,CACtC,eAAe,EACf,OAAO8B,QAAyB,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACxD,IAAI;IACF,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,gBAAgB,EAAEC,OAAO,CAACC,GAAG,CAACC,kBAAkB,CAAC;IACrEJ,GAAG,CAACK,YAAY,CAACC,MAAM,CAAC,SAAS,EAAER,QAAQ,CAACS,MAAM,CAAC;IACnD,IAAIT,QAAQ,CAACU,QAAQ,EAAE;MACrBR,GAAG,CAACK,YAAY,CAACC,MAAM,CAAC,UAAU,EAAER,QAAQ,CAACU,QAAQ,CAAC;IACxD;IACA,MAAMC,QAAQ,GAAG,MAAMxC,SAAS,CAACyC,IAAI,CAACV,GAAG,CAACW,QAAQ,CAAC,CAAC,EAAEb,QAAQ,EAAE;MAAEc,eAAe,EAAE;IAAK,CAAQ,CAAC;IAEjG,OAAOH,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOnB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AACD;AACA,OAAO,MAAMC,YAAY,GAAG/C,gBAAgB,CAC1C,qBAAqB,EACrB,OAAOgD,MAA2B,EAAE;EAAEjB;AAAgB,CAAC,KAAK;EAC1D,IAAI;IACF,MAAMkB,QAAQ,GAAG,MAAM/C,OAAO,CAAC6C,YAAY,CAACC,MAAM,CAAC;IAEnD,OAAOC,QAAQ;EACjB,CAAC,CAAC,OAAOvB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMI,YAAY,GAAGlD,gBAAgB,CAC1C,mBAAmB,EACnB,OAAOgD,MAAqE,EAAE;EAAEjB;AAAgB,CAAC,KAAK;EACpG,IAAI;IACF;IACA,MAAMU,QAAQ,GAAG,MAAMU,KAAK,CAAC,kBAAkB,EAAE;MAC/CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACP,QAAQ,CAACgB,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMb,IAAI,GAAG,MAAMJ,QAAQ,CAACkB,IAAI,CAAC,CAAC;IAClC,OAAOd,IAAI;EACb,CAAC,CAAC,OAAOnB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMc,YAAY,GAAG5D,gBAAgB,CAC1C,mBAAmB,EACnB,OAAOgD,MAAyE,EAAE;EAAEjB;AAAgB,CAAC,KAAK;EACxG,IAAI;IACF;IACA,MAAMU,QAAQ,GAAG,MAAMU,KAAK,CAAC,mBAAmB,EAAE;MAChDC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACP,QAAQ,CAACgB,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMb,IAAI,GAAG,MAAMJ,QAAQ,CAACkB,IAAI,CAAC,CAAC;IAClC,OAAOd,IAAI,CAAC/B,MAAM;EACpB,CAAC,CAAC,OAAOY,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;;AAED;AACA,MAAMe,SAAS,GAAG9D,WAAW,CAAC;EAC5B+D,IAAI,EAAE,MAAM;EACZ3D,YAAY;EACZ4D,QAAQ,EAAE;IACR;IACAC,eAAe,EAAEA,CAACC,KAAK,EAAEC,MAA6B,KAAK;MACzDD,KAAK,CAAC7D,YAAY,GAAG8D,MAAM,CAACC,OAAO;MACnCF,KAAK,CAAC3C,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAED8C,kBAAkB,EAAEA,CAACH,KAAK,EAAEC,MAA6B,KAAK;MAC5DD,KAAK,CAAC5D,eAAe,GAAG6D,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDE,qBAAqB,EAAEA,CAACJ,KAAK,EAAEC,MAA6B,KAAK;MAC/DD,KAAK,CAACxD,kBAAkB,GAAGyD,MAAM,CAACC,OAAO;IAC3C,CAAC;IAEDG,kBAAkB,EAAEA,CAACL,KAAK,EAAEC,MAA8B,KAAK;MAC7DD,KAAK,CAACrC,eAAe,GAAGsC,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDI,WAAW,EAAEA,CAACN,KAAK,EAAEC,MAA8B,KAAK;MACtDD,KAAK,CAAC3D,QAAQ,GAAG4D,MAAM,CAACC,OAAO;IACjC,CAAC;IAEDK,SAAS,EAAEA,CAACP,KAAK,EAAEC,MAA8B,KAAK;MACpDD,KAAK,CAAC1D,MAAM,GAAG2D,MAAM,CAACC,OAAO;IAC/B,CAAC;IAED;IACAM,kBAAkB,EAAEA,CAACR,KAAK,EAAEC,MAAsC,KAAK;MACrED,KAAK,CAACzD,eAAe,GAAG0D,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDO,YAAY,EAAEA,CAACT,KAAK,EAAEC,MAAiC,KAAK;MAC1DD,KAAK,CAACvD,SAAS,GAAGwD,MAAM,CAACC,OAAO;IAClC,CAAC;IAEDQ,uBAAuB,EAAEA,CAACV,KAAK,EAAEC,MAA6B,KAAK;MACjED,KAAK,CAACtD,oBAAoB,GAAGuD,MAAM,CAACC,OAAO;IAC7C,CAAC;IAEDS,mBAAmB,EAAEA,CAACX,KAAK,EAAEC,MAA+B,KAAK;MAC/DD,KAAK,CAACrD,OAAO,GAAGqD,KAAK,CAACrD,OAAO,CAACiE,GAAG,CAACC,MAAM,IAAI;QAC1C,MAAMC,YAAY,GAAGb,MAAM,CAACC,OAAO,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,MAAM,CAACI,GAAG,CAAC;QACnE,OAAOH,YAAY,GAAG;UAAE,GAAGD,MAAM;UAAE,GAAGC;QAAa,CAAC,GAAGD,MAAM;MAC/D,CAAC,CAAC;IACJ,CAAC;IAEDK,qBAAqB,EAAGlB,KAAK,IAAK;MAChCA,KAAK,CAACrD,OAAO,GAAGqD,KAAK,CAACrD,OAAO,CAACiE,GAAG,CAACC,MAAM,KAAK;QAC3C,GAAGA,MAAM;QACTM,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAEDC,YAAY,EAAGrB,KAAK,IAAK;MACvBA,KAAK,CAAC3C,qBAAqB,IAAI,CAAC;IAClC,CAAC;IAEDiE,wBAAwB,EAAEA,CAACtB,KAAK,EAAEC,MAA6B,KAAK;MAClED,KAAK,CAAC3C,qBAAqB,GAAG4C,MAAM,CAACC,OAAO;IAC9C,CAAC;IACD;IACAqB,UAAU,EAAEA,CAACvB,KAAK,EAAEC,MAA4C,KAAK;MACnED,KAAK,CAACrD,OAAO,GAAGqD,KAAK,CAACrD,OAAO,CAACiE,GAAG,CAACC,MAAM,IAAI;QAC1C,MAAMW,MAAM,GAAGvB,MAAM,CAACC,OAAO,CAACa,IAAI,CAACU,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACR,GAAG,KAAKJ,MAAM,CAACI,GAAG,CAAC;QAClE,OAAOO,MAAM,GAAG;UAAE,GAAGX,MAAM;UAAE,GAAGW;QAAO,CAAC,GAAGX,MAAM;MACnD,CAAC,CAAC;IACJ,CAAC;IAEDa,gBAAgB,EAAEA,CAAC1B,KAAK,EAAEC,MAAiC,KAAK;MAC9DD,KAAK,CAACpD,aAAa,GAAGqD,MAAM,CAACC,OAAO;IACtC,CAAC;IAEDyB,YAAY,EAAEA,CAAC3B,KAAK,EAAEC,MAAoE,KAAK;MAC7F,MAAM2B,WAAW,GAAG5B,KAAK,CAACrD,OAAO,CAACkF,SAAS,CAACJ,CAAC,IAAIA,CAAC,CAACR,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAACe,GAAG,CAAC;MAC9E,IAAIW,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB5B,KAAK,CAACrD,OAAO,CAACiF,WAAW,CAAC,GAAG;UAAE,GAAG5B,KAAK,CAACrD,OAAO,CAACiF,WAAW,CAAC;UAAE,GAAG3B,MAAM,CAACC,OAAO,CAAC4B;QAAQ,CAAC;MAC3F;IACF,CAAC;IAEDC,SAAS,EAAEA,CAAC/B,KAAK,EAAEC,MAAiC,KAAK;MACvD,MAAM+B,aAAa,GAAGhC,KAAK,CAACrD,OAAO,CAACkF,SAAS,CAACJ,CAAC,IAAIA,CAAC,CAACR,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAACe,GAAG,CAAC;MAChF,IAAIe,aAAa,KAAK,CAAC,CAAC,EAAE;QACxBhC,KAAK,CAACrD,OAAO,CAACsF,IAAI,CAAChC,MAAM,CAACC,OAAO,CAAC;MACpC;IACF,CAAC;IAEDgC,YAAY,EAAEA,CAAClC,KAAK,EAAEC,MAA6B,KAAK;MACtDD,KAAK,CAACrD,OAAO,GAAGqD,KAAK,CAACrD,OAAO,CAACwF,MAAM,CAACV,CAAC,IAAIA,CAAC,CAACR,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAAC;IACrE,CAAC;IAEDkC,eAAe,EAAEA,CAACpC,KAAK,EAAEC,MAAuD,KAAK;MACnF,IAAID,KAAK,CAACpD,aAAa,EAAE;QACvBoD,KAAK,CAACpD,aAAa,CAACuE,MAAM,GAAGlB,MAAM,CAACC,OAAO,CAACiB,MAAM;QAClDnB,KAAK,CAACpD,aAAa,CAACwE,IAAI,GAAGnB,MAAM,CAACC,OAAO,CAACkB,IAAI;MAChD;IACF,CAAC;IAED;IACAiB,SAAS,EAAEA,CAACrC,KAAK,EAAEC,MAA8B,KAAK;MACpDD,KAAK,CAACnD,MAAM,GAAGoD,MAAM,CAACC,OAAO;IAC/B,CAAC;IAEDoC,aAAa,EAAEA,CAACtC,KAAK,EAAEC,MAAgC,KAAK;MAC1DD,KAAK,CAAClD,UAAU,GAAGmD,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACAqC,aAAa,EAAEA,CAACvC,KAAK,EAAEC,MAAiD,KAAK;MAC3E,IAAIA,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QAC3BF,KAAK,CAACjD,UAAU,GAAG,IAAI;MACzB,CAAC,MAAM;QACLiD,KAAK,CAACjD,UAAU,GAAG;UACjB,IAAIiD,KAAK,CAACjD,UAAU,IAAI,CAAC,CAAC,CAAC;UAC3B,GAAGkD,MAAM,CAACC;QACZ,CAAC;MACH;IACF,CAAC;IAEDsC,aAAa,EAAEA,CAACxC,KAAK,EAAEC,MAAiD,KAAK;MAC3E,IAAIA,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QAC3BF,KAAK,CAAChD,UAAU,GAAG,IAAI;MACzB,CAAC,MAAM;QACLgD,KAAK,CAAChD,UAAU,GAAG;UACjB,IAAIgD,KAAK,CAAChD,UAAU,IAAI,CAAC,CAAC,CAAC;UAC3B,GAAGiD,MAAM,CAACC;QACZ,CAAC;MACH;IACF,CAAC;IAED;IACAuC,OAAO,EAAEA,CAACzC,KAAK,EAAEC,MAAqD,KAAK;MACzED,KAAK,CAAC/C,IAAI,GAAGgD,MAAM,CAACC,OAAO;IAC7B,CAAC;IAEDwC,YAAY,EAAEA,CAAC1C,KAAK,EAAEC,MAA6B,KAAK;MACtDD,KAAK,CAAC9C,SAAS,GAAG+C,MAAM,CAACC,OAAO;IAClC,CAAC;IAED;IACAyC,YAAY,EAAEA,CAAC3C,KAAK,EAAEC,MAA8B,KAAK;MACvDD,KAAK,CAAC7C,SAAS,GAAG8C,MAAM,CAACC,OAAO;IAClC,CAAC;IAED0C,cAAc,EAAEA,CAAC5C,KAAK,EAAEC,MAA6B,KAAK;MACxDD,KAAK,CAAC5C,WAAW,GAAG6C,MAAM,CAACC,OAAO;IACpC,CAAC;IAED2C,aAAa,EAAEA,CAAC7C,KAAK,EAAEC,MAA8B,KAAK;MACxDD,KAAK,CAAC1C,UAAU,GAAG2C,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACA4C,SAAS,EAAG9C,KAAK,IAAK;MACpB,OAAO;QAAE,GAAG9D,YAAY;QAAEI,MAAM,EAAE0D,KAAK,CAAC1D;MAAO,CAAC;IAClD,CAAC;IAED;IACAyG,UAAU,EAAG/C,KAAK,IAAK;MACrBA,KAAK,CAACzC,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B;EACF,CAAC;EAEDuF,aAAa,EAAGC,OAAO,IAAK;IAC1B;IACAA,OAAO,CACJC,OAAO,CAACtF,QAAQ,CAACuF,OAAO,EAAGnD,KAAK,IAAK;MACpCA,KAAK,CAACtC,OAAO,CAACF,SAAS,GAAG,IAAI;MAC9BwC,KAAK,CAACtC,OAAO,CAACD,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDyF,OAAO,CAACtF,QAAQ,CAACwF,SAAS,EAAE,CAACpD,KAAK,EAAEC,MAAM,KAAK;MAC9CD,KAAK,CAACtC,OAAO,CAACF,SAAS,GAAG,KAAK;MAC/BwC,KAAK,CAACrD,OAAO,GAAGsD,MAAM,CAACC,OAAO,CAACvD,OAAO;MACtCqD,KAAK,CAACpD,aAAa,GAAG;QACpB,GAAGoD,KAAK,CAACpD,aAAa;QACtB,GAAGqD,MAAM,CAACoD,IAAI,CAACC,GAAG;QAClBrC,GAAG,EAAEhB,MAAM,CAACC,OAAO,CAACe;MACtB,CAAC;MACDjB,KAAK,CAAC1D,MAAM,GAAG,KAAK;IACtB,CAAC,CAAC,CACD4G,OAAO,CAACtF,QAAQ,CAAC2F,QAAQ,EAAE,CAACvD,KAAK,EAAEC,MAAM,KAAK;MAC7CD,KAAK,CAACtC,OAAO,CAACF,SAAS,GAAG,KAAK;MAC/BwC,KAAK,CAACtC,OAAO,CAACD,KAAK,GAAGwC,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;IACJ;IACA+C,OAAO,CACJC,OAAO,CAACpE,YAAY,CAACqE,OAAO,EAAGnD,KAAK,IAAK;MACxCA,KAAK,CAACzC,OAAO,CAACC,SAAS,GAAG,IAAI;MAC9BwC,KAAK,CAACzC,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDyF,OAAO,CAACpE,YAAY,CAACsE,SAAS,EAAE,CAACpD,KAAK,EAAEC,MAAM,KAAK;MAClDD,KAAK,CAACzC,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/BwC,KAAK,CAACzD,eAAe,GAAG0D,MAAM,CAACC,OAAO;MACtCF,KAAK,CAACtD,oBAAoB,GAAGuD,MAAM,CAACC,OAAO,CAACiB,MAAM;IACpD,CAAC,CAAC,CACD+B,OAAO,CAACpE,YAAY,CAACyE,QAAQ,EAAE,CAACvD,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAACzC,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/BwC,KAAK,CAACzC,OAAO,CAACE,KAAK,GAAGwC,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA+C,OAAO,CACJC,OAAO,CAACjE,YAAY,CAACkE,OAAO,EAAGnD,KAAK,IAAK;MACxCA,KAAK,CAACzC,OAAO,CAACC,SAAS,GAAG,IAAI;IAChC,CAAC,CAAC,CACD0F,OAAO,CAACjE,YAAY,CAACmE,SAAS,EAAE,CAACpD,KAAK,EAAEC,MAAM,KAAK;MAClDD,KAAK,CAACzC,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B;IACF,CAAC,CAAC,CACD0F,OAAO,CAACjE,YAAY,CAACsE,QAAQ,EAAE,CAACvD,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAACzC,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/BwC,KAAK,CAACzC,OAAO,CAACE,KAAK,GAAGwC,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA+C,OAAO,CACJC,OAAO,CAACvD,YAAY,CAACyD,SAAS,EAAE,CAACpD,KAAK,EAAEC,MAAM,KAAK;MAClDD,KAAK,CAACnD,MAAM,GAAGoD,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC,CACDgD,OAAO,CAACvD,YAAY,CAAC4D,QAAQ,EAAE,CAACvD,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAACzC,OAAO,CAACE,KAAK,GAAGwC,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXH,eAAe;EACfO,WAAW;EACXC,SAAS;EACTC,kBAAkB;EAClBC,YAAY;EACZC,uBAAuB;EACvBW,YAAY;EACZC,wBAAwB;EACxBC,UAAU;EACVG,gBAAgB;EAChBU,eAAe;EACfzB,mBAAmB;EACnBO,qBAAqB;EACrBS,YAAY;EACZI,SAAS;EACTG,YAAY;EACZG,SAAS;EACTC,aAAa;EACbC,aAAa;EACbC,aAAa;EACbC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,cAAc;EACdE,SAAS;EACTC,UAAU;EACV1C,kBAAkB;EAClBwC;AACF,CAAC,GAAGjD,SAAS,CAAC4D,OAAO;AAErB,eAAe5D,SAAS,CAAC6D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}