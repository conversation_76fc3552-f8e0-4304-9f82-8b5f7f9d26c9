// Game Redux slice
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { GameState, Question, Score, PlayerData, Round2Grid, Round4Grid, ScoreRule, RoomPlayer, JoinRoomRequest, Answer, GetQuestionsRequest } from '../../../shared/types';
import apiClient from '../../../shared/services/api/client';
import { set } from 'firebase/database';
import gameApi from '../../../shared/services/game/gameApi';

// Initial state
const initialState: GameState = {
  // Current game status
  currentRound: "1",
  currentTestName: "",
  isActive: false,
  isHost: false,

  // Questions and answers
  currentQuestion: null,
  selectedPacketName: "",
  questions: [],
  currentCorrectAnswer: "",
  // Players and scoring
  players: [],
  currentPlayer: null,
  scores: [],
  scoreRules: null,

  // Round-specific data
  round2Grid: null,
  round4Grid: null,

  // Game settings
  mode: 'manual',
  timeLimit: 30,

  // UI state
  showRules: false,
  currentTurn: 0,
  currentQuestionNumber: 1,
  isBuzzOpen: false,

  // Loading states
  loading: {
    isLoading: false,
    error: null,
  },

  // joing states
  joining: {
    isLoading: false,
    error: null,
  },

  //input disabled
  isInputDisabled: true,
};


export const joinRoom = createAsyncThunk(
  'room/joinRoom',
  async (joinData: JoinRoomRequest, { rejectWithValue }) => {
    try {
      const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);
      url.searchParams.append('room_id', joinData.roomId);
      if (joinData.password) {
        url.searchParams.append('password', joinData.password);
      }
      const response = await apiClient.post(url.toString(), joinData, { _isAuthRequired: true } as any);

      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);
// Async thunks
export const getQuestions = createAsyncThunk(
  'game/fetchQuestions',
  async (params: GetQuestionsRequest, { rejectWithValue }) => {
    try {
      const question = await gameApi.getQuestions(params);

      return question;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const submitAnswer = createAsyncThunk(
  'game/submitAnswer',
  async (params: { roomId: string; uid: string; answer: string; time: number }, { rejectWithValue }) => {
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/game/answer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error('Failed to submit answer');
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const updateScores = createAsyncThunk(
  'game/updateScores',
  async (params: { roomId: string; mode: string; scores?: Score[]; round: string }, { rejectWithValue }) => {
    try {
      // TODO: Replace with actual API call
      const response = await fetch('/api/game/scoring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        throw new Error('Failed to update scores');
      }

      const data = await response.json();
      return data.scores;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// Game slice
const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    // Game state management
    setCurrentRound: (state, action: PayloadAction<string>) => {
      state.currentRound = action.payload;
      state.currentQuestionNumber = 1; // Reset question number when round changes
    },

    setCurrentTestName: (state, action: PayloadAction<string>) => {
      state.currentTestName = action.payload;
    },

    setSelectedPacketName: (state, action: PayloadAction<string>) => {
      state.selectedPacketName = action.payload;
    },

    setIsInputDisabled: (state, action: PayloadAction<boolean>) => {
      state.isInputDisabled = action.payload;
    },

    setIsActive: (state, action: PayloadAction<boolean>) => {
      state.isActive = action.payload;
    },

    setIsHost: (state, action: PayloadAction<boolean>) => {
      state.isHost = action.payload;
    },

    // Question management
    setCurrentQuestion: (state, action: PayloadAction<Question | null>) => {
      state.currentQuestion = action.payload;
    },

    setQuestions: (state, action: PayloadAction<Question[]>) => {
      state.questions = action.payload;
    },

    setCurrentCorrectAnswer: (state, action: PayloadAction<string>) => {
      state.currentCorrectAnswer = action.payload;
    },

    setPlayerAnswerList: (state, action: PayloadAction<Answer[]>) => {
      state.players = state.players.map(player => {
        const answerUpdate = action.payload.find(a => a.uid === player.uid);
        return answerUpdate ? { ...player, ...answerUpdate } : player;
      });
    },

    clearPlayerAnswerList: (state) => {
      state.players = state.players.map(player => ({
        ...player,
        answer: "",
        time: 0,
      }));
    },

    nextQuestion: (state) => {
      state.currentQuestionNumber += 1;
    },

    setCurrentQuestionNumber: (state, action: PayloadAction<number>) => {
      state.currentQuestionNumber = action.payload;
    },
    // Player management
    setPlayers: (state, action: PayloadAction<Partial<PlayerData[]>>) => {
      state.players = state.players.map(player => {
        const update = action.payload.find(p => p && p.uid === player.uid);
        return update ? { ...player, ...update } : player;
      });
    },

    setCurrentPlayer: (state, action: PayloadAction<RoomPlayer>) => {
      state.currentPlayer = action.payload;
    },

    updatePlayer: (state, action: PayloadAction<{ uid: string; updates: Partial<PlayerData> }>) => {
      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);
      if (playerIndex !== -1) {
        state.players[playerIndex] = { ...state.players[playerIndex], ...action.payload.updates };
      }
    },

    addPlayer: (state, action: PayloadAction<PlayerData>) => {
      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);
      if (existingIndex === -1) {
        state.players.push(action.payload);
      }
    },

    removePlayer: (state, action: PayloadAction<string>) => {
      state.players = state.players.filter(p => p.uid !== action.payload);
    },

    setPlayerAnswer: (state, action: PayloadAction<{ answer: string; time: number }>) => {
      if (state.currentPlayer) {
        state.currentPlayer.answer = action.payload.answer;
        state.currentPlayer.time = action.payload.time;
      }
    },

    // Scoring
    setScores: (state, action: PayloadAction<Score[]>) => {
      state.scores = action.payload;
    },

    setScoreRules: (state, action: PayloadAction<ScoreRule>) => {
      state.scoreRules = action.payload;
    },

    // Round-specific data
    setRound2Grid: (state, action: PayloadAction<Partial<Round2Grid | null>>) => {
      state.round2Grid = action.payload;
    },

    setRound4Grid: (state, action: PayloadAction<Partial<Round4Grid | null>>) => {
      state.round4Grid = action.payload;
    },

    // Game settings
    setMode: (state, action: PayloadAction<'manual' | 'auto' | 'adaptive'>) => {
      state.mode = action.payload;
    },

    setTimeLimit: (state, action: PayloadAction<number>) => {
      state.timeLimit = action.payload;
    },

    // UI state
    setShowRules: (state, action: PayloadAction<boolean>) => {
      state.showRules = action.payload;
    },

    setCurrentTurn: (state, action: PayloadAction<number>) => {
      state.currentTurn = action.payload;
    },

    setIsBuzzOpen: (state, action: PayloadAction<boolean>) => {
      state.isBuzzOpen = action.payload;
    },

    // Reset game state
    resetGame: (state) => {
      return { ...initialState, isHost: state.isHost };
    },

    // Error handling
    clearError: (state) => {
      state.loading.error = null;
    },
  },

  extraReducers: (builder) => {
    // Join room
    builder
      .addCase(joinRoom.pending, (state) => {
        state.joining.isLoading = true;
        state.joining.error = null;
      })
      .addCase(joinRoom.fulfilled, (state, action) => {
        state.joining.isLoading = false;
        state.players = action.payload.players;
        state.currentPlayer = {
          ...state.currentPlayer,
          ...action.meta.arg,
          uid: action.payload.uid
        }
        state.isHost = false;
      })
      .addCase(joinRoom.rejected, (state, action) => {
        state.joining.isLoading = false;
        state.joining.error = action.payload as string;
      });
    // Fetch questions
    builder
      .addCase(getQuestions.pending, (state) => {
        state.loading.isLoading = true;
        state.loading.error = null;
      })
      .addCase(getQuestions.fulfilled, (state, action) => {
        state.loading.isLoading = false;
        state.currentQuestion = action.payload;
        state.currentCorrectAnswer = action.payload.answer
      })
      .addCase(getQuestions.rejected, (state, action) => {
        state.loading.isLoading = false;
        state.loading.error = action.payload as string;
      });

    // Submit answer
    builder
      .addCase(submitAnswer.pending, (state) => {
        state.loading.isLoading = true;
      })
      .addCase(submitAnswer.fulfilled, (state, action) => {
        state.loading.isLoading = false;
        // Handle answer submission result
      })
      .addCase(submitAnswer.rejected, (state, action) => {
        state.loading.isLoading = false;
        state.loading.error = action.payload as string;
      });

    // Update scores
    builder
      .addCase(updateScores.fulfilled, (state, action) => {
        state.scores = action.payload;
      })
      .addCase(updateScores.rejected, (state, action) => {
        state.loading.error = action.payload as string;
      });
  },
});

export const {
  setCurrentRound,
  setIsActive,
  setIsHost,
  setCurrentQuestion,
  setQuestions,
  setCurrentCorrectAnswer,
  nextQuestion,
  setCurrentQuestionNumber,
  setPlayers,
  setCurrentPlayer,
  setPlayerAnswer,
  setPlayerAnswerList,
  clearPlayerAnswerList,
  updatePlayer,
  addPlayer,
  removePlayer,
  setScores,
  setScoreRules,
  setRound2Grid,
  setRound4Grid,
  setMode,
  setTimeLimit,
  setShowRules,
  setCurrentTurn,
  resetGame,
  clearError,
  setIsInputDisabled,
  setIsBuzzOpen
} = gameSlice.actions;

export default gameSlice.reducer;
