{"ast": null, "code": "// Game API service\nimport { api } from '../api/client';\nimport { API_ENDPOINTS } from '../../constants';\nexport const gameApi = {\n  /**\r\n   * Get questions for a specific round\r\n   */\n  async getQuestions(params) {\n    const response = await api.get(API_ENDPOINTS.GAME.QUESTION, {\n      params: {\n        test_name: params.testName,\n        round: params.round,\n        question_number: params.questionNumber,\n        packet_name: params === null || params === void 0 ? void 0 : params.packetName,\n        difficulty: params === null || params === void 0 ? void 0 : params.difficulty\n      }\n    });\n    return response.data.data;\n  },\n  /**\r\n   * Get prefetch question\r\n   */\n  async getPrefetchQuestion(params) {\n    const response = await api.get(API_ENDPOINTS.GAME.PREFETCH, {\n      params: {\n        test_name: params.testName,\n        round: params.round,\n        question_number: params.questionNumber\n      }\n    });\n    return response.data.data;\n  },\n  /**\r\n   * Get packet names\r\n   */\n  async getPacketNames(testName) {\n    const response = await api.get(API_ENDPOINTS.GAME.PACKETS, {\n      params: {\n        test_name: testName\n      }\n    });\n    return response.data.data;\n  },\n  /**\r\n   * Send grid to players\r\n   */\n  async sendGrid(params) {\n    const response = await api.post(`${API_ENDPOINTS.GAME.GRID}?room_id=${params.roomId}`, {\n      grid: params.grid\n    });\n    return response.data.data.success;\n  },\n  /**\r\n   * Start a new round\r\n   */\n  async startRound(params) {\n    await api.post(`${API_ENDPOINTS.GAME.ROUND_START}?room_id=${params.roomId}&round=${params.round}`, {\n      grid: params.grid\n    });\n  },\n  /**\r\n   * Submit player answer\r\n   */\n  async submitAnswer(data, room_id) {\n    const response = await api.post(API_ENDPOINTS.GAME.SUBMIT, data, {\n      params: {\n        room_id: room_id\n      },\n      _isAuthRequired: true\n    });\n    return response.data;\n  },\n  /**\r\n   * Broadcast player answers\r\n   */\n  async broadcastAnswers(roomId) {\n    const response = await api.post(`${API_ENDPOINTS.GAME.BROADCAST_ANSWER}?room_id=${roomId}`);\n    return response.data.data;\n  },\n  /**\r\n   * Update game scoring\r\n   */\n  async updateScoring(params) {\n    const response = await api.post(`${API_ENDPOINTS.GAME.SCORING}?room_id=${params.roomId}`, {\n      mode: params.mode,\n      scores: params.scores,\n      round: params.round,\n      stt: params.stt,\n      is_obstacle_correct: params.isObstacleCorrect,\n      obstacle_point: params.obstaclePoint,\n      is_correct: params.isCorrect,\n      round_4_mode: params.round4Mode,\n      difficulty: params.difficulty,\n      is_take_turn_correct: params.isTakeTurnCorrect,\n      stt_take_turn: params.sttTakeTurn,\n      stt_taken: params.sttTaken\n    });\n    return response.data.data;\n  },\n  /**\r\n   * Update current turn\r\n   */\n  async updateTurn(roomId, turn) {\n    await api.post(`${API_ENDPOINTS.GAME.TURN}?room_id=${roomId}&turn=${turn}`);\n  },\n  /**\r\n   * Show game rules\r\n   */\n  async showRules(roomId, roundNumber) {\n    await api.post(`${API_ENDPOINTS.GAME.RULES}/show?room_id=${roomId}&round_number=${roundNumber}`);\n  },\n  /**\r\n   * Hide game rules\r\n   */\n  async hideRules(roomId) {\n    await api.post(`${API_ENDPOINTS.GAME.RULES}/hide?room_id=${roomId}`);\n  },\n  /**\r\n   * Set selected packet name\r\n   */\n  async setSelectedPacketName(roomId, packetName) {\n    await api.post(`${API_ENDPOINTS.GAME.PACKETS}/select?room_id=${roomId}&packet_name=${packetName}`);\n  }\n};\nexport default gameApi;", "map": {"version": 3, "names": ["api", "API_ENDPOINTS", "gameApi", "getQuestions", "params", "response", "get", "GAME", "QUESTION", "test_name", "testName", "round", "question_number", "questionNumber", "packet_name", "packetName", "difficulty", "data", "getPrefetchQuestion", "PREFETCH", "getPacketNames", "PACKETS", "sendGrid", "post", "GRID", "roomId", "grid", "success", "startRound", "ROUND_START", "submitAnswer", "room_id", "SUBMIT", "_isAuthRequired", "broadcastAnswers", "BROADCAST_ANSWER", "updateScoring", "SCORING", "mode", "scores", "stt", "is_obstacle_correct", "isObstacleCorrect", "obstacle_point", "obstaclePoint", "is_correct", "isCorrect", "round_4_mode", "round4Mode", "is_take_turn_correct", "isTakeTurnCorrect", "stt_take_turn", "sttTakeTurn", "stt_taken", "sttTaken", "updateTurn", "turn", "TURN", "showRules", "roundNumber", "RULES", "hideRules", "setSelectedPacketName"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/services/game/gameApi.ts"], "sourcesContent": ["// Game API service\r\nimport { api } from '../api/client';\r\nimport { API_ENDPOINTS } from '../../constants';\r\nimport { \r\n  GetQuestionsRequest,\r\n  GetQuestionsResponse,\r\n  SubmitAnswerRequest,\r\n  SubmitAnswerResponse,\r\n  ScoringRequest,\r\n  ScoringResponse,\r\n  SendGridRequest,\r\n  SendGridResponse,\r\n  Question,\r\n  Score\r\n} from '../../types';\r\n\r\nexport const gameApi = {\r\n  /**\r\n   * Get questions for a specific round\r\n   */\r\n  async getQuestions(params: GetQuestionsRequest): Promise<Question[]> {\r\n    const response = await api.get<Question[]>(API_ENDPOINTS.GAME.QUESTION, {\r\n      params: {\r\n        test_name: params.testName,\r\n        round: params.round,\r\n        question_number: params.questionNumber,\r\n        packet_name: params?.packetName,\r\n        difficulty: params?.difficulty,\r\n      },\r\n    });\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Get prefetch question\r\n   */\r\n  async getPrefetchQuestion(params: { testName: string; round: number; questionNumber: number }): Promise<Question> {\r\n    const response = await api.get<Question>(API_ENDPOINTS.GAME.PREFETCH, {\r\n      params: {\r\n        test_name: params.testName,\r\n        round: params.round,\r\n        question_number: params.questionNumber,\r\n      },\r\n    });\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Get packet names\r\n   */\r\n  async getPacketNames(testName: string): Promise<string[]> {\r\n    const response = await api.get<string[]>(API_ENDPOINTS.GAME.PACKETS, {\r\n      params: { test_name: testName },\r\n    });\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Send grid to players\r\n   */\r\n  async sendGrid(params: SendGridRequest): Promise<boolean> {\r\n    const response = await api.post<SendGridResponse['data']>(\r\n      `${API_ENDPOINTS.GAME.GRID}?room_id=${params.roomId}`,\r\n      { grid: params.grid }\r\n    );\r\n    return response.data.data.success;\r\n  },\r\n\r\n  /**\r\n   * Start a new round\r\n   */\r\n  async startRound(params: { roomId: string; round: string; grid?: string[][] }): Promise<void> {\r\n    await api.post(\r\n      `${API_ENDPOINTS.GAME.ROUND_START}?room_id=${params.roomId}&round=${params.round}`,\r\n      { grid: params.grid }\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Submit player answer\r\n   */\r\n  async submitAnswer(data: SubmitAnswerRequest, room_id: string): Promise<SubmitAnswerResponse> {\r\n    const response = await api.post<any>(\r\n      API_ENDPOINTS.GAME.SUBMIT,\r\n      data,\r\n      { \r\n        params: { room_id: room_id },\r\n        _isAuthRequired: true\r\n      }\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  /**\r\n   * Broadcast player answers\r\n   */\r\n  async broadcastAnswers(roomId: string): Promise<any[]> {\r\n    const response = await api.post<any[]>(\r\n      `${API_ENDPOINTS.GAME.BROADCAST_ANSWER}?room_id=${roomId}`\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Update game scoring\r\n   */\r\n  async updateScoring(params: ScoringRequest): Promise<Score[]> {\r\n    const response = await api.post<Score[]>(\r\n      `${API_ENDPOINTS.GAME.SCORING}?room_id=${params.roomId}`,\r\n      {\r\n        mode: params.mode,\r\n        scores: params.scores,\r\n        round: params.round,\r\n        stt: params.stt,\r\n        is_obstacle_correct: params.isObstacleCorrect,\r\n        obstacle_point: params.obstaclePoint,\r\n        is_correct: params.isCorrect,\r\n        round_4_mode: params.round4Mode,\r\n        difficulty: params.difficulty,\r\n        is_take_turn_correct: params.isTakeTurnCorrect,\r\n        stt_take_turn: params.sttTakeTurn,\r\n        stt_taken: params.sttTaken,\r\n      }\r\n    );\r\n    return response.data.data;\r\n  },\r\n\r\n  /**\r\n   * Update current turn\r\n   */\r\n  async updateTurn(roomId: string, turn: number): Promise<void> {\r\n    await api.post(\r\n      `${API_ENDPOINTS.GAME.TURN}?room_id=${roomId}&turn=${turn}`\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Show game rules\r\n   */\r\n  async showRules(roomId: string, roundNumber: string): Promise<void> {\r\n    await api.post(\r\n      `${API_ENDPOINTS.GAME.RULES}/show?room_id=${roomId}&round_number=${roundNumber}`\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Hide game rules\r\n   */\r\n  async hideRules(roomId: string): Promise<void> {\r\n    await api.post(\r\n      `${API_ENDPOINTS.GAME.RULES}/hide?room_id=${roomId}`\r\n    );\r\n  },\r\n\r\n  /**\r\n   * Set selected packet name\r\n   */\r\n  async setSelectedPacketName(roomId: string, packetName: string): Promise<void> {\r\n    await api.post(\r\n      `${API_ENDPOINTS.GAME.PACKETS}/select?room_id=${roomId}&packet_name=${packetName}`\r\n    );\r\n  },\r\n};\r\n\r\nexport default gameApi;\r\n"], "mappings": "AAAA;AACA,SAASA,GAAG,QAAQ,eAAe;AACnC,SAASC,aAAa,QAAQ,iBAAiB;AAc/C,OAAO,MAAMC,OAAO,GAAG;EACrB;AACF;AACA;EACE,MAAMC,YAAYA,CAACC,MAA2B,EAAuB;IACnE,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAaL,aAAa,CAACM,IAAI,CAACC,QAAQ,EAAE;MACtEJ,MAAM,EAAE;QACNK,SAAS,EAAEL,MAAM,CAACM,QAAQ;QAC1BC,KAAK,EAAEP,MAAM,CAACO,KAAK;QACnBC,eAAe,EAAER,MAAM,CAACS,cAAc;QACtCC,WAAW,EAAEV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEW,UAAU;QAC/BC,UAAU,EAAEZ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEY;MACtB;IACF,CAAC,CAAC;IACF,OAAOX,QAAQ,CAACY,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMC,mBAAmBA,CAACd,MAAmE,EAAqB;IAChH,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAWL,aAAa,CAACM,IAAI,CAACY,QAAQ,EAAE;MACpEf,MAAM,EAAE;QACNK,SAAS,EAAEL,MAAM,CAACM,QAAQ;QAC1BC,KAAK,EAAEP,MAAM,CAACO,KAAK;QACnBC,eAAe,EAAER,MAAM,CAACS;MAC1B;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACY,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMG,cAAcA,CAACV,QAAgB,EAAqB;IACxD,MAAML,QAAQ,GAAG,MAAML,GAAG,CAACM,GAAG,CAAWL,aAAa,CAACM,IAAI,CAACc,OAAO,EAAE;MACnEjB,MAAM,EAAE;QAAEK,SAAS,EAAEC;MAAS;IAChC,CAAC,CAAC;IACF,OAAOL,QAAQ,CAACY,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMK,QAAQA,CAAClB,MAAuB,EAAoB;IACxD,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACuB,IAAI,CAC7B,GAAGtB,aAAa,CAACM,IAAI,CAACiB,IAAI,YAAYpB,MAAM,CAACqB,MAAM,EAAE,EACrD;MAAEC,IAAI,EAAEtB,MAAM,CAACsB;IAAK,CACtB,CAAC;IACD,OAAOrB,QAAQ,CAACY,IAAI,CAACA,IAAI,CAACU,OAAO;EACnC,CAAC;EAED;AACF;AACA;EACE,MAAMC,UAAUA,CAACxB,MAA4D,EAAiB;IAC5F,MAAMJ,GAAG,CAACuB,IAAI,CACZ,GAAGtB,aAAa,CAACM,IAAI,CAACsB,WAAW,YAAYzB,MAAM,CAACqB,MAAM,UAAUrB,MAAM,CAACO,KAAK,EAAE,EAClF;MAAEe,IAAI,EAAEtB,MAAM,CAACsB;IAAK,CACtB,CAAC;EACH,CAAC;EAED;AACF;AACA;EACE,MAAMI,YAAYA,CAACb,IAAyB,EAAEc,OAAe,EAAiC;IAC5F,MAAM1B,QAAQ,GAAG,MAAML,GAAG,CAACuB,IAAI,CAC7BtB,aAAa,CAACM,IAAI,CAACyB,MAAM,EACzBf,IAAI,EACJ;MACEb,MAAM,EAAE;QAAE2B,OAAO,EAAEA;MAAQ,CAAC;MAC5BE,eAAe,EAAE;IACnB,CACF,CAAC;IACD,OAAO5B,QAAQ,CAACY,IAAI;EACtB,CAAC;EAED;AACF;AACA;EACE,MAAMiB,gBAAgBA,CAACT,MAAc,EAAkB;IACrD,MAAMpB,QAAQ,GAAG,MAAML,GAAG,CAACuB,IAAI,CAC7B,GAAGtB,aAAa,CAACM,IAAI,CAAC4B,gBAAgB,YAAYV,MAAM,EAC1D,CAAC;IACD,OAAOpB,QAAQ,CAACY,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMmB,aAAaA,CAAChC,MAAsB,EAAoB;IAC5D,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACuB,IAAI,CAC7B,GAAGtB,aAAa,CAACM,IAAI,CAAC8B,OAAO,YAAYjC,MAAM,CAACqB,MAAM,EAAE,EACxD;MACEa,IAAI,EAAElC,MAAM,CAACkC,IAAI;MACjBC,MAAM,EAAEnC,MAAM,CAACmC,MAAM;MACrB5B,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnB6B,GAAG,EAAEpC,MAAM,CAACoC,GAAG;MACfC,mBAAmB,EAAErC,MAAM,CAACsC,iBAAiB;MAC7CC,cAAc,EAAEvC,MAAM,CAACwC,aAAa;MACpCC,UAAU,EAAEzC,MAAM,CAAC0C,SAAS;MAC5BC,YAAY,EAAE3C,MAAM,CAAC4C,UAAU;MAC/BhC,UAAU,EAAEZ,MAAM,CAACY,UAAU;MAC7BiC,oBAAoB,EAAE7C,MAAM,CAAC8C,iBAAiB;MAC9CC,aAAa,EAAE/C,MAAM,CAACgD,WAAW;MACjCC,SAAS,EAAEjD,MAAM,CAACkD;IACpB,CACF,CAAC;IACD,OAAOjD,QAAQ,CAACY,IAAI,CAACA,IAAI;EAC3B,CAAC;EAED;AACF;AACA;EACE,MAAMsC,UAAUA,CAAC9B,MAAc,EAAE+B,IAAY,EAAiB;IAC5D,MAAMxD,GAAG,CAACuB,IAAI,CACZ,GAAGtB,aAAa,CAACM,IAAI,CAACkD,IAAI,YAAYhC,MAAM,SAAS+B,IAAI,EAC3D,CAAC;EACH,CAAC;EAED;AACF;AACA;EACE,MAAME,SAASA,CAACjC,MAAc,EAAEkC,WAAmB,EAAiB;IAClE,MAAM3D,GAAG,CAACuB,IAAI,CACZ,GAAGtB,aAAa,CAACM,IAAI,CAACqD,KAAK,iBAAiBnC,MAAM,iBAAiBkC,WAAW,EAChF,CAAC;EACH,CAAC;EAED;AACF;AACA;EACE,MAAME,SAASA,CAACpC,MAAc,EAAiB;IAC7C,MAAMzB,GAAG,CAACuB,IAAI,CACZ,GAAGtB,aAAa,CAACM,IAAI,CAACqD,KAAK,iBAAiBnC,MAAM,EACpD,CAAC;EACH,CAAC;EAED;AACF;AACA;EACE,MAAMqC,qBAAqBA,CAACrC,MAAc,EAAEV,UAAkB,EAAiB;IAC7E,MAAMf,GAAG,CAACuB,IAAI,CACZ,GAAGtB,aAAa,CAACM,IAAI,CAACc,OAAO,mBAAmBI,MAAM,gBAAgBV,UAAU,EAClF,CAAC;EACH;AACF,CAAC;AAED,eAAeb,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}