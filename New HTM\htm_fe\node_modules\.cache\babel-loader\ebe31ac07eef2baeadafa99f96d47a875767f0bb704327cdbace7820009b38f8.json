{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\HostManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useHost } from '../context/hostContext';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { usePlayer } from '../context/playerContext';\nimport { playSound } from './services';\nimport http from '../services/http';\nimport { CheckCircleIcon, ArrowRightCircleIcon, EyeIcon, ClockIcon, PlayCircleIcon, SpeakerWaveIcon, MusicalNoteIcon, DocumentTextIcon, EyeSlashIcon, QuestionMarkCircleIcon } from \"@heroicons/react/24/solid\";\nimport { toast } from 'react-toastify';\nimport HostQuestionPreview from './HostQuestionPreview';\nimport HostGuideModal from './HostGuideModal';\nimport PlayerColorSelector from './PlayerColorSelector';\nimport useTokenRefresh from '../hooks/useTokenRefresh';\nimport useGameApi from '../shared/hooks/api/useGameApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HostManagement = () => {\n  _s();\n  const {\n    handleNextQuestion,\n    handleShowAnswer,\n    handleStartTime,\n    handleStartRound,\n    handleCorrectAnswer,\n    currentAnswer,\n    playerScores,\n    setPlayerScores,\n    currentQuestionIndex,\n    setCurrentQuestionIndex,\n    hostInitialGrid,\n    playerColors,\n    setPlayerColors,\n    inGameQuestionIndex,\n    setInGameQuestionIndex\n  } = useHost();\n  const {\n    initialGrid,\n    selectedTopic,\n    easyQuestionNumber,\n    mediumQuestionNumber,\n    hardQuestionNumber,\n    setEasyQuestionNumber,\n    setMediumQuestionNumber,\n    setHardQuestionNumber,\n    level,\n    setAnswerList\n  } = usePlayer();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const currentRound = searchParams.get(\"round\") || \"1\";\n  const testName = searchParams.get(\"testName\") || \"1\";\n  const roomId = searchParams.get(\"roomId\") || \"1\";\n  const [showingRules, setShowingRules] = useState(false);\n  const [showGuideModal, setShowGuideModal] = useState(false);\n  const [showColorSelector, setShowColorSelector] = useState(false);\n  const {\n    getQuestions\n  } = useGameApi();\n\n  // Initialize token refresh for host\n  useTokenRefresh();\n  // const handleRoundChange = async (delta: number) => {\n  //     console.log(\"currentRound\", currentRound)\n  //     const newRound = parseInt(currentRound) + delta;\n  //     console.log(\"new round\", newRound)\n  //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\n  //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\n  //     }\n\n  //     // Clear frontend state\n  //     setAnswerList([]);\n\n  //     // Clear Firebase data\n  //     await deletePath(roomId, \"questions\");\n  //     await deletePath(roomId, \"answers\");\n  //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\n  //     await deletePath(roomId, \"turn\"); // Clear turn assignments\n  //     await deletePath(roomId, \"isModified\"); // Clear isModified state\n  //     // Don't clear showRules here - let host control modal display manually\n  //     setShowingRules(false); // Reset rules button state\n  // };\n\n  const handleToggleRules = async () => {\n    try {\n      if (showingRules) {\n        // Hide rules\n        await http.post('game/rules/hide', true, {}, {\n          room_id: roomId\n        });\n        setShowingRules(false);\n        toast.success('Đã ẩn luật thi');\n      } else {\n        // Show rules\n        await http.post('room/rules/show', true, {}, {\n          room_id: roomId,\n          round_number: currentRound\n        });\n        setShowingRules(true);\n        toast.success(`Đã hiển thị luật thi vòng ${currentRound}`);\n      }\n    } catch (error) {\n      console.error('Error toggling rules:', error);\n      toast.error('Lỗi khi thay đổi hiển thị luật thi');\n    }\n  };\n  const handleSavePlayerColors = colors => {\n    setPlayerColors(colors);\n    toast.success('Đã lưu màu cho thí sinh!');\n  };\n  useEffect(() => {\n    setInGameQuestionIndex(1);\n    // Clear rules when entering new round to prevent auto-show\n    setShowingRules(false);\n    // Also clear rules from Firebase to ensure clean state\n    http.post('game/rules/hide', true, {}, {\n      room_id: roomId\n    }).catch(console.error);\n  }, [currentRound]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-4 lg:p-6 mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(HostQuestionPreview, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowGuideModal(true),\n          className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 font-medium text-sm\",\n          title: \"H\\u01B0\\u1EDBng d\\u1EABn host\",\n          children: [/*#__PURE__*/_jsxDEV(QuestionMarkCircleIcon, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 25\n          }, this), \"H\\u01B0\\u1EDBng d\\u1EABn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-400 text-sm\",\n        children: [\"V\\xF2ng \", currentRound, \" - \", currentRound === \"1\" ? \"NHỔ NEO\" : currentRound === \"2\" ? \"VƯỢT SÓNG\" : currentRound === \"3\" ? \"BỨT PHÁ\" : currentRound === \"4\" ? \"CHINH PHỤC\" : \"PHÂN LƯỢT\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [currentRound !== \"4\" && /*#__PURE__*/_jsxDEV(\"input\", {\n          min: 0,\n          value: inGameQuestionIndex,\n          onChange: e => {\n            const val = e.target.value;\n            if (val === \"\") {\n              setInGameQuestionIndex(0);\n            } else {\n              setInGameQuestionIndex(Number(val));\n            }\n          },\n          className: \"w-16 px-2 py-2 rounded-lg border border-blue-400 bg-slate-700 text-white text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-400\",\n          style: {\n            minWidth: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 25\n        }, this), currentRound !== \"4\" && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: async () => {\n            try {\n              setCurrentQuestionIndex(inGameQuestionIndex.toString());\n\n              // Fetch and display the specified question\n              if (currentRound === \"3\") {\n                await handleNextQuestion(selectedTopic, undefined, inGameQuestionIndex.toString());\n              } else if (currentRound === \"4\") {\n                await handleNextQuestion(undefined, level, inGameQuestionIndex.toString());\n              } else {\n                await handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\n              }\n              toast.success(`Đã chuyển đến câu hỏi số: ${inGameQuestionIndex}`);\n            } catch (error) {\n              console.error(\"Error jumping to question:\", error);\n              toast.error(\"Lỗi khi chuyển đến câu hỏi!\");\n            }\n          },\n          className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowRightCircleIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 29\n          }, this), \"CHUY\\u1EC2N \\u0110\\u1EBEN C\\xC2U H\\u1ECEI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            console.log(\"playerScores before starting round\", playerScores);\n            const newScoreList = [...playerScores];\n            for (let score of newScoreList) {\n              score[\"isCorrect\"] = false;\n              score[\"isModified\"] = false;\n            }\n            setPlayerScores(newScoreList);\n            //updateScore(roomId, playerScores, \"manual\", currentRound)\n            handleStartRound(currentRound, roomId, initialGrid);\n            toast.success(`Đã bắt đầu vòng thi ${currentRound}`);\n          },\n          className: \"w-full flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base\",\n          children: [/*#__PURE__*/_jsxDEV(PlayCircleIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 21\n          }, this), \"B\\u1EAET \\u0110\\u1EA6U V\\xD2NG THI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (currentRound === \"3\") {\n            // Use the input box value for round 3\n            handleNextQuestion(selectedTopic, undefined, \"0\");\n            setInGameQuestionIndex(prev => prev + 1);\n            return;\n          }\n          if (currentRound === \"4\") {\n            if (level === \"Dễ\") {\n              handleNextQuestion(undefined, level, easyQuestionNumber.toString());\n              setEasyQuestionNumber(prev => prev + 1);\n            }\n            if (level === \"Trung bình\") {\n              handleNextQuestion(undefined, level, (20 + mediumQuestionNumber).toString());\n              setMediumQuestionNumber(prev => prev + 1);\n            }\n            if (level === \"Khó\") {\n              handleNextQuestion(undefined, level, (40 + hardQuestionNumber).toString());\n              setHardQuestionNumber(prev => prev + 1);\n            }\n            return;\n          }\n          // Use the input box value for other rounds\n          handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\n          setInGameQuestionIndex(prev => prev + 1);\n        },\n        className: \"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ArrowRightCircleIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 25\n        }, this), \"C\\xC2U H\\u1ECEI TI\\u1EBEP THEO\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          handleStartTime();\n          toast.success(\"Đã bắt đầu đếm giờ!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 21\n        }, this), \"B\\u1EAET \\u0110\\u1EA6U \\u0110\\u1EBEM GI\\u1EDC\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          handleShowAnswer();\n          toast.success(\"Đã hiển thị câu trả lời cho người chơi!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(EyeIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), \"HI\\u1EC6N C\\xC2U TR\\u1EA2 L\\u1EDCI TH\\xCD SINH\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handleCorrectAnswer(currentAnswer),\n        className: \"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 21\n        }, this), \"HI\\u1EC6N \\u0110\\xC1P \\xC1N \\u0110\\xDANG\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          playSound(roomId, currentRound);\n          toast.success(`Đã chạy âm thanh cho vòng thi ${currentRound}`);\n        },\n        className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(SpeakerWaveIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this), \"CH\\u1EA0Y \\xC2M THANH B\\u1EAET \\u0110\\u1EA6U V\\xD2NG THI\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          playSound(roomId, \"opening\");\n          toast.success(\"Đã chạy âm thanh mở đầu!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(MusicalNoteIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 21\n        }, this), \"CH\\u1EA0Y \\xC2M THANH M\\u1EDE \\u0110\\u1EA6U\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleToggleRules,\n        className: `flex items-center ${showingRules ? 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-red-400/50' : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-green-400/50'} text-white p-2 lg:p-3 rounded-lg shadow-md border transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full`,\n        children: [showingRules ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 25\n        }, this), showingRules ? 'ẨN LUẬT THI' : 'HIỂN THỊ LUẬT THI']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(HostGuideModal, {\n      isOpen: showGuideModal,\n      onClose: () => setShowGuideModal(false),\n      round: currentRound\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(PlayerColorSelector, {\n      isOpen: showColorSelector,\n      onClose: () => setShowColorSelector(false),\n      players: playerScores,\n      onSaveColors: handleSavePlayerColors,\n      currentColors: playerColors\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 9\n  }, this);\n};\n_s(HostManagement, \"OLXHrfRSztZM2NZXxwDBPmyi9Lg=\", false, function () {\n  return [useHost, usePlayer, useSearchParams, useNavigate, useGameApi, useTokenRefresh];\n});\n_c = HostManagement;\nexport default HostManagement;\nvar _c;\n$RefreshReg$(_c, \"HostManagement\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useHost", "useSearchParams", "useNavigate", "usePlayer", "playSound", "http", "CheckCircleIcon", "ArrowRightCircleIcon", "EyeIcon", "ClockIcon", "PlayCircleIcon", "SpeakerWaveIcon", "MusicalNoteIcon", "DocumentTextIcon", "EyeSlashIcon", "QuestionMarkCircleIcon", "toast", "HostQuestionPreview", "HostGuideModal", "PlayerColorSelector", "useTokenRefresh", "useGameApi", "jsxDEV", "_jsxDEV", "HostManagement", "_s", "handleNextQuestion", "handleShowAnswer", "handleStartTime", "handleStartRound", "handleCorrectAnswer", "currentAnswer", "playerScores", "setPlayerScores", "currentQuestionIndex", "setCurrentQuestionIndex", "hostInitialGrid", "playerColors", "setPlayerColors", "inGameQuestionIndex", "setInGameQuestionIndex", "initialGrid", "selectedTopic", "easyQuestionNumber", "mediumQuestionNumber", "hardQuestionNumber", "setEasyQuestionNumber", "setMediumQuestionNumber", "setHardQuestionNumber", "level", "setAnswerList", "searchParams", "navigate", "currentRound", "get", "testName", "roomId", "showingRules", "setShowingRules", "showGuideModal", "setShowGuideModal", "showColorSelector", "setShowColorSelector", "getQuestions", "handleToggleRules", "post", "room_id", "success", "round_number", "error", "console", "handleSavePlayerColors", "colors", "catch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "min", "value", "onChange", "e", "val", "target", "Number", "style", "min<PERSON><PERSON><PERSON>", "toString", "undefined", "log", "newScoreList", "score", "prev", "isOpen", "onClose", "round", "players", "onSaveColors", "currentColors", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/HostManagement.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\r\nimport { useHost } from '../context/hostContext';\r\nimport { useSearchParams, useNavigate } from 'react-router-dom';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { openBuzz } from './services';\r\nimport { playSound } from './services';\r\nimport { deletePath } from '../services/firebaseServices';\r\nimport { updateScore } from '../pages/Host/Management/service';\r\nimport http from '../services/http';\r\nimport {\r\n    CheckCircleIcon,\r\n    BellAlertIcon,\r\n    ArrowRightCircleIcon,\r\n    EyeIcon,\r\n    ClockIcon,\r\n    PlayCircleIcon,\r\n    SpeakerWaveIcon,\r\n    MusicalNoteIcon,\r\n    DocumentTextIcon,\r\n    EyeSlashIcon,\r\n    QuestionMarkCircleIcon,\r\n    PaintBrushIcon,\r\n} from \"@heroicons/react/24/solid\";\r\nimport { toast } from 'react-toastify';\r\nimport HostQuestionPreview from './HostQuestionPreview';\r\nimport HostGuideModal from './HostGuideModal';\r\nimport PlayerColorSelector from './PlayerColorSelector';\r\nimport useTokenRefresh from '../hooks/useTokenRefresh';\r\nimport useGameApi from '../shared/hooks/api/useGameApi';\r\n\r\n\r\nconst HostManagement = () => {\r\n    const {\r\n        handleNextQuestion,\r\n        handleShowAnswer,\r\n        handleStartTime,\r\n        handleStartRound,\r\n        handleCorrectAnswer,\r\n        currentAnswer,\r\n        playerScores,\r\n        setPlayerScores,\r\n        currentQuestionIndex,\r\n        setCurrentQuestionIndex,\r\n        hostInitialGrid,\r\n        playerColors,\r\n        setPlayerColors,\r\n        inGameQuestionIndex,\r\n        setInGameQuestionIndex\r\n    } = useHost();\r\n\r\n    const { initialGrid, selectedTopic, easyQuestionNumber, mediumQuestionNumber, hardQuestionNumber, setEasyQuestionNumber, setMediumQuestionNumber, setHardQuestionNumber, level, setAnswerList } = usePlayer()\r\n\r\n    const [searchParams] = useSearchParams();\r\n    const navigate = useNavigate();\r\n\r\n    const currentRound = searchParams.get(\"round\") || \"1\";\r\n    const testName = searchParams.get(\"testName\") || \"1\"\r\n    const roomId = searchParams.get(\"roomId\") || \"1\"\r\n    const [showingRules, setShowingRules] = useState(false);\r\n    const [showGuideModal, setShowGuideModal] = useState(false);\r\n    const [showColorSelector, setShowColorSelector] = useState(false);\r\n\r\n    const {getQuestions, } = useGameApi()\r\n\r\n    // Initialize token refresh for host\r\n    useTokenRefresh();\r\n    // const handleRoundChange = async (delta: number) => {\r\n    //     console.log(\"currentRound\", currentRound)\r\n    //     const newRound = parseInt(currentRound) + delta;\r\n    //     console.log(\"new round\", newRound)\r\n    //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\r\n    //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\r\n    //     }\r\n\r\n    //     // Clear frontend state\r\n    //     setAnswerList([]);\r\n\r\n    //     // Clear Firebase data\r\n    //     await deletePath(roomId, \"questions\");\r\n    //     await deletePath(roomId, \"answers\");\r\n    //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\r\n    //     await deletePath(roomId, \"turn\"); // Clear turn assignments\r\n    //     await deletePath(roomId, \"isModified\"); // Clear isModified state\r\n    //     // Don't clear showRules here - let host control modal display manually\r\n    //     setShowingRules(false); // Reset rules button state\r\n    // };\r\n\r\n    const handleToggleRules = async () => {\r\n        try {\r\n            if (showingRules) {\r\n                // Hide rules\r\n                await http.post('game/rules/hide', true, {}, { room_id: roomId });\r\n                setShowingRules(false);\r\n                toast.success('Đã ẩn luật thi');\r\n            } else {\r\n                // Show rules\r\n                await http.post('room/rules/show', true, {}, {\r\n                    room_id: roomId,\r\n                    round_number: currentRound\r\n                });\r\n                setShowingRules(true);\r\n                toast.success(`Đã hiển thị luật thi vòng ${currentRound}`);\r\n            }\r\n        } catch (error) {\r\n            console.error('Error toggling rules:', error);\r\n            toast.error('Lỗi khi thay đổi hiển thị luật thi');\r\n        }\r\n    };\r\n\r\n    const handleSavePlayerColors = (colors: Record<string, string>) => {\r\n        setPlayerColors(colors);\r\n        toast.success('Đã lưu màu cho thí sinh!');\r\n    };\r\n\r\n    useEffect(() => {\r\n        setInGameQuestionIndex(1);\r\n        // Clear rules when entering new round to prevent auto-show\r\n        setShowingRules(false);\r\n        // Also clear rules from Firebase to ensure clean state\r\n        http.post('game/rules/hide', true, {}, { room_id: roomId }).catch(console.error);\r\n    }, [currentRound]);\r\n\r\n    return (\r\n        <div className=\"bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-4 lg:p-6 mt-4\">\r\n\r\n            {/* Host Question Preview */}\r\n            <HostQuestionPreview />\r\n\r\n            {/* Guide and Color Selection */}\r\n            <div className=\"flex items-center justify-between mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <button\r\n                        onClick={() => setShowGuideModal(true)}\r\n                        className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 font-medium text-sm\"\r\n                        title=\"Hướng dẫn host\"\r\n                    >\r\n                        <QuestionMarkCircleIcon className=\"w-5 h-5 mr-2\" />\r\n                        Hướng dẫn\r\n                    </button>\r\n\r\n                </div>\r\n\r\n                <div className=\"text-gray-400 text-sm\">\r\n                    Vòng {currentRound} - {currentRound === \"1\" ? \"NHỔ NEO\" : currentRound === \"2\" ? \"VƯỢT SÓNG\" : currentRound === \"3\" ? \"BỨT PHÁ\" : currentRound === \"4\" ? \"CHINH PHỤC\" : \"PHÂN LƯỢT\"}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Host actions - First row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4 mb-4\">\r\n\r\n                {/* <button\r\n                    onClick={() => openBuzz(roomId)}\r\n                    className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-3 lg:p-4 rounded-xl shadow-lg border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <BellAlertIcon className=\"w-5 h-5 mr-2\" />\r\n\r\n                    MỞ BẤM CHUÔNG\r\n                </button> */}\r\n                <div className=\"flex items-center gap-3\">\r\n                    {/* Current Question Index Input - Disabled for Round 4 */}\r\n                    {currentRound !== \"4\" && (\r\n                        <input\r\n                            min={0}\r\n                            value={inGameQuestionIndex}\r\n                            onChange={e => {\r\n                                const val = e.target.value;\r\n                                if (val === \"\") {\r\n                                    setInGameQuestionIndex(0);\r\n                                } else {\r\n                                    setInGameQuestionIndex(Number(val));\r\n                                }\r\n                            }}\r\n                            className=\"w-16 px-2 py-2 rounded-lg border border-blue-400 bg-slate-700 text-white text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-400\"\r\n                            style={{ minWidth: 0 }}\r\n                        />\r\n                    )}\r\n                    {currentRound !== \"4\" && (\r\n                        <button\r\n                        onClick={async () => {\r\n                            try {\r\n                                setCurrentQuestionIndex(inGameQuestionIndex.toString());\r\n\r\n                                // Fetch and display the specified question\r\n                                if (currentRound === \"3\") {\r\n                                    await handleNextQuestion(selectedTopic, undefined, inGameQuestionIndex.toString());\r\n                                } else if (currentRound === \"4\") {\r\n                                    await handleNextQuestion(undefined, level, inGameQuestionIndex.toString());\r\n                                } else {\r\n                                    await handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\r\n                                }\r\n\r\n                                toast.success(`Đã chuyển đến câu hỏi số: ${inGameQuestionIndex}`);\r\n                            } catch (error) {\r\n                                console.error(\"Error jumping to question:\", error);\r\n                                toast.error(\"Lỗi khi chuyển đến câu hỏi!\");\r\n                            }\r\n                        }}\r\n                        className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                    >\r\n                            <ArrowRightCircleIcon className=\"w-4 h-4 mr-2\" />\r\n                            CHUYỂN ĐẾN CÂU HỎI\r\n                        </button>\r\n                    )}\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-3\">\r\n                    <button\r\n                    onClick={() => {\r\n                        console.log(\"playerScores before starting round\", playerScores);\r\n\r\n                        const newScoreList = [...playerScores]\r\n                        for (let score of newScoreList) {\r\n                            score[\"isCorrect\"] = false;\r\n                            score[\"isModified\"] = false\r\n                        }\r\n                        setPlayerScores(newScoreList)\r\n                        //updateScore(roomId, playerScores, \"manual\", currentRound)\r\n                        handleStartRound(currentRound, roomId, initialGrid)\r\n                        toast.success(`Đã bắt đầu vòng thi ${currentRound}`);\r\n                    }}\r\n                    className=\"w-full flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base\"\r\n                >\r\n                    <PlayCircleIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    BẮT ĐẦU VÒNG THI\r\n                </button>\r\n                    {/* Current Question Index Input */}\r\n                    \r\n                </div>\r\n                <button\r\n                        onClick={() => {\r\n                            if (currentRound === \"3\") {\r\n                                // Use the input box value for round 3\r\n                                handleNextQuestion(selectedTopic, undefined, \"0\")\r\n                                setInGameQuestionIndex((prev: number) => prev + 1);\r\n                                return\r\n                            }\r\n                            if (currentRound === \"4\") {\r\n                                if (level === \"Dễ\") {\r\n                                    handleNextQuestion(undefined, level, easyQuestionNumber.toString())\r\n                                    setEasyQuestionNumber((prev: number) => (prev + 1))\r\n                                }\r\n                                if (level === \"Trung bình\") {\r\n                                    handleNextQuestion(undefined, level, (20 + mediumQuestionNumber).toString())\r\n                                    setMediumQuestionNumber((prev: number) => (prev + 1))\r\n                                }\r\n                                if (level === \"Khó\") {\r\n                                    handleNextQuestion(undefined, level, (40 + hardQuestionNumber).toString())\r\n                                    setHardQuestionNumber((prev: number) => (prev + 1))\r\n                                }\r\n                                return\r\n                            }\r\n                            // Use the input box value for other rounds\r\n                            handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString())\r\n                            setInGameQuestionIndex((prev: number) => prev + 1);\r\n                        }}\r\n                        className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                    >\r\n                        <ArrowRightCircleIcon className=\"w-4 h-4 mr-2\" />\r\n                        CÂU HỎI TIẾP THEO\r\n                    </button>\r\n                    <button\r\n                    onClick={() => {\r\n                        handleStartTime()\r\n                        toast.success(\"Đã bắt đầu đếm giờ!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <ClockIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    BẮT ĐẦU ĐẾM GIỜ\r\n                </button>\r\n                <button\r\n                    onClick={() => {\r\n                        handleShowAnswer()\r\n                        toast.success(\"Đã hiển thị câu trả lời cho người chơi!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <EyeIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    HIỆN CÂU TRẢ LỜI THÍ SINH\r\n                </button>\r\n               \r\n            </div>\r\n\r\n            {/* Show Answer and Start Timer - Second row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4 mb-4\">\r\n                 <button\r\n                    onClick={() => handleCorrectAnswer(currentAnswer)}\r\n                    className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <CheckCircleIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    HIỆN ĐÁP ÁN ĐÚNG\r\n                </button>\r\n               \r\n\r\n            </div>\r\n\r\n            {/* Sound controls - Fourth row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4\">\r\n                <button\r\n                    onClick={() => {\r\n                        playSound(roomId, currentRound)\r\n                        toast.success(`Đã chạy âm thanh cho vòng thi ${currentRound}`);\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <SpeakerWaveIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    CHẠY ÂM THANH BẮT ĐẦU VÒNG THI\r\n                </button>\r\n                <button\r\n                    onClick={() => {\r\n                        playSound(roomId, \"opening\")\r\n                        toast.success(\"Đã chạy âm thanh mở đầu!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <MusicalNoteIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    CHẠY ÂM THANH MỞ ĐẦU\r\n                </button>\r\n                <button\r\n                    onClick={handleToggleRules}\r\n                    className={`flex items-center ${\r\n                        showingRules\r\n                            ? 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-red-400/50'\r\n                            : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-green-400/50'\r\n                    } text-white p-2 lg:p-3 rounded-lg shadow-md border transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full`}\r\n                >\r\n                    {showingRules ? (\r\n                        <EyeSlashIcon className=\"w-4 h-4 mr-2\" />\r\n                    ) : (\r\n                        <DocumentTextIcon className=\"w-4 h-4 mr-2\" />\r\n                    )}\r\n                    {showingRules ? 'ẨN LUẬT THI' : 'HIỂN THỊ LUẬT THI'}\r\n                </button>\r\n            </div>\r\n\r\n            {/* Modals */}\r\n            <HostGuideModal\r\n                isOpen={showGuideModal}\r\n                onClose={() => setShowGuideModal(false)}\r\n                round={currentRound}\r\n            />\r\n\r\n            <PlayerColorSelector\r\n                isOpen={showColorSelector}\r\n                onClose={() => setShowColorSelector(false)}\r\n                players={playerScores}\r\n                onSaveColors={handleSavePlayerColors}\r\n                currentColors={playerColors}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HostManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,SAAS,QAAQ,YAAY;AAGtC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,SACIC,eAAe,EAEfC,oBAAoB,EACpBC,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,YAAY,EACZC,sBAAsB,QAEnB,2BAA2B;AAClC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,UAAU,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGxD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IACFC,kBAAkB;IAClBC,gBAAgB;IAChBC,eAAe;IACfC,gBAAgB;IAChBC,mBAAmB;IACnBC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,uBAAuB;IACvBC,eAAe;IACfC,YAAY;IACZC,eAAe;IACfC,mBAAmB;IACnBC;EACJ,CAAC,GAAGxC,OAAO,CAAC,CAAC;EAEb,MAAM;IAAEyC,WAAW;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC,qBAAqB;IAAEC,uBAAuB;IAAEC,qBAAqB;IAAEC,KAAK;IAAEC;EAAc,CAAC,GAAG/C,SAAS,CAAC,CAAC;EAE7M,MAAM,CAACgD,YAAY,CAAC,GAAGlD,eAAe,CAAC,CAAC;EACxC,MAAMmD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAE9B,MAAMmD,YAAY,GAAGF,YAAY,CAACG,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EACrD,MAAMC,QAAQ,GAAGJ,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG;EACpD,MAAME,MAAM,GAAGL,YAAY,CAACG,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG;EAChD,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAM;IAACgE;EAAc,CAAC,GAAG1C,UAAU,CAAC,CAAC;;EAErC;EACAD,eAAe,CAAC,CAAC;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM4C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,IAAIP,YAAY,EAAE;QACd;QACA,MAAMpD,IAAI,CAAC4D,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;UAAEC,OAAO,EAAEV;QAAO,CAAC,CAAC;QACjEE,eAAe,CAAC,KAAK,CAAC;QACtB1C,KAAK,CAACmD,OAAO,CAAC,gBAAgB,CAAC;MACnC,CAAC,MAAM;QACH;QACA,MAAM9D,IAAI,CAAC4D,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;UACzCC,OAAO,EAAEV,MAAM;UACfY,YAAY,EAAEf;QAClB,CAAC,CAAC;QACFK,eAAe,CAAC,IAAI,CAAC;QACrB1C,KAAK,CAACmD,OAAO,CAAC,6BAA6Bd,YAAY,EAAE,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CrD,KAAK,CAACqD,KAAK,CAAC,oCAAoC,CAAC;IACrD;EACJ,CAAC;EAED,MAAME,sBAAsB,GAAIC,MAA8B,IAAK;IAC/DlC,eAAe,CAACkC,MAAM,CAAC;IACvBxD,KAAK,CAACmD,OAAO,CAAC,0BAA0B,CAAC;EAC7C,CAAC;EAEDrE,SAAS,CAAC,MAAM;IACZ0C,sBAAsB,CAAC,CAAC,CAAC;IACzB;IACAkB,eAAe,CAAC,KAAK,CAAC;IACtB;IACArD,IAAI,CAAC4D,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;MAAEC,OAAO,EAAEV;IAAO,CAAC,CAAC,CAACiB,KAAK,CAACH,OAAO,CAACD,KAAK,CAAC;EACpF,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;EAElB,oBACI9B,OAAA;IAAKmD,SAAS,EAAC,kGAAkG;IAAAC,QAAA,gBAG7GpD,OAAA,CAACN,mBAAmB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBxD,OAAA;MAAKmD,SAAS,EAAC,kGAAkG;MAAAC,QAAA,gBAC7GpD,OAAA;QAAKmD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxCpD,OAAA;UACIyD,OAAO,EAAEA,CAAA,KAAMpB,iBAAiB,CAAC,IAAI,CAAE;UACvCc,SAAS,EAAC,kNAAkN;UAC5NO,KAAK,EAAC,+BAAgB;UAAAN,QAAA,gBAEtBpD,OAAA,CAACR,sBAAsB;YAAC2D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC9B,EAACtB,YAAY,EAAC,KAAG,EAACA,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGA,YAAY,KAAK,GAAG,GAAG,WAAW,GAAGA,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGA,YAAY,KAAK,GAAG,GAAG,YAAY,GAAG,WAAW;MAAA;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAU9CpD,OAAA;QAAKmD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GAEnCtB,YAAY,KAAK,GAAG,iBACjB9B,OAAA;UACI2D,GAAG,EAAE,CAAE;UACPC,KAAK,EAAE5C,mBAAoB;UAC3B6C,QAAQ,EAAEC,CAAC,IAAI;YACX,MAAMC,GAAG,GAAGD,CAAC,CAACE,MAAM,CAACJ,KAAK;YAC1B,IAAIG,GAAG,KAAK,EAAE,EAAE;cACZ9C,sBAAsB,CAAC,CAAC,CAAC;YAC7B,CAAC,MAAM;cACHA,sBAAsB,CAACgD,MAAM,CAACF,GAAG,CAAC,CAAC;YACvC;UACJ,CAAE;UACFZ,SAAS,EAAC,oJAAoJ;UAC9Je,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAE;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACJ,EACA1B,YAAY,KAAK,GAAG,iBACjB9B,OAAA;UACAyD,OAAO,EAAE,MAAAA,CAAA,KAAY;YACjB,IAAI;cACA7C,uBAAuB,CAACI,mBAAmB,CAACoD,QAAQ,CAAC,CAAC,CAAC;;cAEvD;cACA,IAAItC,YAAY,KAAK,GAAG,EAAE;gBACtB,MAAM3B,kBAAkB,CAACgB,aAAa,EAAEkD,SAAS,EAAErD,mBAAmB,CAACoD,QAAQ,CAAC,CAAC,CAAC;cACtF,CAAC,MAAM,IAAItC,YAAY,KAAK,GAAG,EAAE;gBAC7B,MAAM3B,kBAAkB,CAACkE,SAAS,EAAE3C,KAAK,EAAEV,mBAAmB,CAACoD,QAAQ,CAAC,CAAC,CAAC;cAC9E,CAAC,MAAM;gBACH,MAAMjE,kBAAkB,CAACkE,SAAS,EAAEA,SAAS,EAAErD,mBAAmB,CAACoD,QAAQ,CAAC,CAAC,CAAC;cAClF;cAEA3E,KAAK,CAACmD,OAAO,CAAC,6BAA6B5B,mBAAmB,EAAE,CAAC;YACrE,CAAC,CAAC,OAAO8B,KAAK,EAAE;cACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;cAClDrD,KAAK,CAACqD,KAAK,CAAC,6BAA6B,CAAC;YAC9C;UACJ,CAAE;UACFK,SAAS,EAAC,uQAAuQ;UAAAC,QAAA,gBAE7QpD,OAAA,CAAChB,oBAAoB;YAACmE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6CAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAENxD,OAAA;QAAKmD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpCpD,OAAA;UACAyD,OAAO,EAAEA,CAAA,KAAM;YACXV,OAAO,CAACuB,GAAG,CAAC,oCAAoC,EAAE7D,YAAY,CAAC;YAE/D,MAAM8D,YAAY,GAAG,CAAC,GAAG9D,YAAY,CAAC;YACtC,KAAK,IAAI+D,KAAK,IAAID,YAAY,EAAE;cAC5BC,KAAK,CAAC,WAAW,CAAC,GAAG,KAAK;cAC1BA,KAAK,CAAC,YAAY,CAAC,GAAG,KAAK;YAC/B;YACA9D,eAAe,CAAC6D,YAAY,CAAC;YAC7B;YACAjE,gBAAgB,CAACwB,YAAY,EAAEG,MAAM,EAAEf,WAAW,CAAC;YACnDzB,KAAK,CAACmD,OAAO,CAAC,uBAAuBd,YAAY,EAAE,CAAC;UACxD,CAAE;UACFqB,SAAS,EAAC,kQAAkQ;UAAAC,QAAA,gBAE5QpD,OAAA,CAACb,cAAc;YAACgE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sCAG/C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGJ,CAAC,eACNxD,OAAA;QACQyD,OAAO,EAAEA,CAAA,KAAM;UACX,IAAI3B,YAAY,KAAK,GAAG,EAAE;YACtB;YACA3B,kBAAkB,CAACgB,aAAa,EAAEkD,SAAS,EAAE,GAAG,CAAC;YACjDpD,sBAAsB,CAAEwD,IAAY,IAAKA,IAAI,GAAG,CAAC,CAAC;YAClD;UACJ;UACA,IAAI3C,YAAY,KAAK,GAAG,EAAE;YACtB,IAAIJ,KAAK,KAAK,IAAI,EAAE;cAChBvB,kBAAkB,CAACkE,SAAS,EAAE3C,KAAK,EAAEN,kBAAkB,CAACgD,QAAQ,CAAC,CAAC,CAAC;cACnE7C,qBAAqB,CAAEkD,IAAY,IAAMA,IAAI,GAAG,CAAE,CAAC;YACvD;YACA,IAAI/C,KAAK,KAAK,YAAY,EAAE;cACxBvB,kBAAkB,CAACkE,SAAS,EAAE3C,KAAK,EAAE,CAAC,EAAE,GAAGL,oBAAoB,EAAE+C,QAAQ,CAAC,CAAC,CAAC;cAC5E5C,uBAAuB,CAAEiD,IAAY,IAAMA,IAAI,GAAG,CAAE,CAAC;YACzD;YACA,IAAI/C,KAAK,KAAK,KAAK,EAAE;cACjBvB,kBAAkB,CAACkE,SAAS,EAAE3C,KAAK,EAAE,CAAC,EAAE,GAAGJ,kBAAkB,EAAE8C,QAAQ,CAAC,CAAC,CAAC;cAC1E3C,qBAAqB,CAAEgD,IAAY,IAAMA,IAAI,GAAG,CAAE,CAAC;YACvD;YACA;UACJ;UACA;UACAtE,kBAAkB,CAACkE,SAAS,EAAEA,SAAS,EAAErD,mBAAmB,CAACoD,QAAQ,CAAC,CAAC,CAAC;UACxEnD,sBAAsB,CAAEwD,IAAY,IAAKA,IAAI,GAAG,CAAC,CAAC;QACtD,CAAE;QACFtB,SAAS,EAAC,2PAA2P;QAAAC,QAAA,gBAErQpD,OAAA,CAAChB,oBAAoB;UAACmE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QACAyD,OAAO,EAAEA,CAAA,KAAM;UACXpD,eAAe,CAAC,CAAC;UACjBZ,KAAK,CAACmD,OAAO,CAAC,qBAAqB,CAAC;QACxC,CAAE;QACFO,SAAS,EAAC,oQAAoQ;QAAAC,QAAA,gBAE9QpD,OAAA,CAACd,SAAS;UAACiE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iDAG1C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QACIyD,OAAO,EAAEA,CAAA,KAAM;UACXrD,gBAAgB,CAAC,CAAC;UAClBX,KAAK,CAACmD,OAAO,CAAC,yCAAyC,CAAC;QAC5D,CAAE;QACFO,SAAS,EAAC,kQAAkQ;QAAAC,QAAA,gBAE5QpD,OAAA,CAACf,OAAO;UAACkE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kDAGxC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAER,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC7CpD,OAAA;QACGyD,OAAO,EAAEA,CAAA,KAAMlD,mBAAmB,CAACC,aAAa,CAAE;QAClD2C,SAAS,EAAC,0PAA0P;QAAAC,QAAA,gBAEpQpD,OAAA,CAACjB,eAAe;UAACoE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4CAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGR,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBACzCpD,OAAA;QACIyD,OAAO,EAAEA,CAAA,KAAM;UACX5E,SAAS,CAACoD,MAAM,EAAEH,YAAY,CAAC;UAC/BrC,KAAK,CAACmD,OAAO,CAAC,iCAAiCd,YAAY,EAAE,CAAC;QAClE,CAAE;QACFqB,SAAS,EAAC,uQAAuQ;QAAAC,QAAA,gBAEjRpD,OAAA,CAACZ,eAAe;UAAC+D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4DAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QACIyD,OAAO,EAAEA,CAAA,KAAM;UACX5E,SAAS,CAACoD,MAAM,EAAE,SAAS,CAAC;UAC5BxC,KAAK,CAACmD,OAAO,CAAC,0BAA0B,CAAC;QAC7C,CAAE;QACFO,SAAS,EAAC,gQAAgQ;QAAAC,QAAA,gBAE1QpD,OAAA,CAACX,eAAe;UAAC8D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+CAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA;QACIyD,OAAO,EAAEhB,iBAAkB;QAC3BU,SAAS,EAAE,qBACPjB,YAAY,GACN,gGAAgG,GAChG,kGAAkG,wIAC6B;QAAAkB,QAAA,GAExIlB,YAAY,gBACTlC,OAAA,CAACT,YAAY;UAAC4D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzCxD,OAAA,CAACV,gBAAgB;UAAC6D,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC/C,EACAtB,YAAY,GAAG,aAAa,GAAG,mBAAmB;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNxD,OAAA,CAACL,cAAc;MACX+E,MAAM,EAAEtC,cAAe;MACvBuC,OAAO,EAAEA,CAAA,KAAMtC,iBAAiB,CAAC,KAAK,CAAE;MACxCuC,KAAK,EAAE9C;IAAa;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eAEFxD,OAAA,CAACJ,mBAAmB;MAChB8E,MAAM,EAAEpC,iBAAkB;MAC1BqC,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,KAAK,CAAE;MAC3CsC,OAAO,EAAEpE,YAAa;MACtBqE,YAAY,EAAE9B,sBAAuB;MACrC+B,aAAa,EAAEjE;IAAa;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACtD,EAAA,CAtUID,cAAc;EAAA,QAiBZxB,OAAO,EAEuLG,SAAS,EAEpLF,eAAe,EACrBC,WAAW,EASHmB,UAAU,EAGnCD,eAAe;AAAA;AAAAmF,EAAA,GAlCb/E,cAAc;AAwUpB,eAAeA,cAAc;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}