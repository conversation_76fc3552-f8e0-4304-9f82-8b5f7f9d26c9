{"ast": null, "code": "var _s = $RefreshSig$();\n// // Game API hook\nimport { useCallback } from 'react';\nimport { useAppDispatch, useAppSelector } from '../../../app/store';\nimport { gameApi } from '../../services/game/gameApi';\nexport const useGameApi = () => {\n  _s();\n  const dispatch = useAppDispatch();\n  const {\n    currentCorrectAnswer,\n    round2Grid,\n    round4Grid,\n    currentRound\n    // loading, \n    // currentQuestion, \n    // questions, \n    // scores, \n    // currentRound,\n    // round2Grid,\n    // round4Grid \n  } = useAppSelector(state => state.game);\n\n  /**\r\n   * Get questions for a round\r\n   */\n  //   const getQuestions = useCallback(async (params: GetQuestionsRequest) => {\n  //     try {\n  //       const result = await dispatch(fetchQuestions(params)).unwrap();\n  //       return result;\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, [dispatch]);\n\n  //   /**\n  //    * Get prefetch question\n  //    */\n  //   const getPrefetchQuestion = useCallback(async (params: { \n  //     testName: string; \n  //     round: number; \n  //     questionNumber: number \n  //   }) => {\n  //     try {\n  //       const question = await gameApi.getPrefetchQuestion(params);\n  //       return question;\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, []);\n\n  //   /**\n  //    * Get packet names\n  //    */\n  //   const getPacketNames = useCallback(async (testName: string) => {\n  //     try {\n  //       const packets = await gameApi.getPacketNames(testName);\n  //       return packets;\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, []);\n\n  //   /**\n  //    * Send grid to players\n  //    */\n  //   const sendGrid = useCallback(async (params: SendGridRequest) => {\n  //     try {\n  //       const success = await gameApi.sendGrid(params);\n\n  //       // Update local state based on current round\n  //       if (currentRound === 2) {\n  //         dispatch(setRound2Grid({\n  //           cells: params.grid,\n  //           rows: params.grid.length,\n  //           cols: params.grid[0]?.length || 0,\n  //           horizontalRows: [],\n  //           cnv: '',\n  //           selectedRows: [],\n  //           correctRows: [],\n  //           incorrectRows: [],\n  //         }));\n  //       } else if (currentRound === 4) {\n  //         // Convert grid to Round 4 format\n  //         const round4Cells = params.grid.map((row, rowIndex) =>\n  //           row.map((cell, colIndex) => ({\n  //             id: `${rowIndex}-${colIndex}`,\n  //             question: {} as Question, // Will be populated later\n  //             isSelected: false,\n  //             isAnswered: false,\n  //             difficulty: 'easy' as const,\n  //             points: 20,\n  //           }))\n  //         );\n\n  //         dispatch(setRound4Grid({\n  //           cells: round4Cells,\n  //           selectedDifficulties: [],\n  //           starPositions: [],\n  //         }));\n  //       }\n\n  //       return success;\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, [dispatch, currentRound]);\n\n  /**\r\n   * Start a new round\r\n   */\n  const startRound = useCallback(async roomId => {\n    try {\n      const startRoundParams = {\n        roomId: roomId,\n        round: currentRound,\n        grid: currentRound === \"2\" || currentRound === \"4\" ? currentRound == \"2\" ? round2Grid === null || round2Grid === void 0 ? void 0 : round2Grid.grid : round4Grid === null || round4Grid === void 0 ? void 0 : round4Grid.grid : undefined\n      };\n      await gameApi.startRound(startRoundParams);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Start a new round\r\n   */\n  const startTimer = useCallback(async roomId => {\n    try {\n      const startRoundParams = {\n        roomId: roomId,\n        round: currentRound,\n        grid: currentRound === \"2\" || currentRound === \"4\" ? currentRound == \"2\" ? round2Grid === null || round2Grid === void 0 ? void 0 : round2Grid.grid : round4Grid === null || round4Grid === void 0 ? void 0 : round4Grid.grid : undefined\n      };\n      await gameApi.startRound(startRoundParams);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  //   /**\n  //    * Submit player answer\n  //    */\n  //   const submitPlayerAnswer = useCallback(async (params: SubmitAnswerRequest) => {\n  //     try {\n  //       const result = await dispatch(submitAnswer(params)).unwrap();\n  //       return result;\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, [dispatch]);\n\n  /**\r\n   * Broadcast player answers\r\n   */\n  const broadcastAnswers = useCallback(async roomId => {\n    try {\n      await gameApi.broadcastAnswers(roomId);\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  /**\r\n   * Broadcast player answers\r\n   */\n  const sendCorrectAnswer = useCallback(async roomId => {\n    try {\n      await gameApi.sendCorrectAnswer({\n        answer: currentCorrectAnswer,\n        roomId: roomId\n      });\n    } catch (error) {\n      throw error;\n    }\n  }, []);\n\n  //   /**\n  //    * Update game scoring\n  //    */\n  //   const updateGameScoring = useCallback(async (params: ScoringRequest) => {\n  //     try {\n  //       const result = await dispatch(updateScores(params)).unwrap();\n  //       return result;\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, [dispatch]);\n\n  //   /**\n  //    * Update current turn\n  //    */\n  //   const updateTurn = useCallback(async (roomId: string, turn: number) => {\n  //     try {\n  //       await gameApi.updateTurn(roomId, turn);\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, []);\n\n  //   /**\n  //    * Show game rules\n  //    */\n  //   const showRules = useCallback(async (roomId: string, roundNumber: string) => {\n  //     try {\n  //       await gameApi.showRules(roomId, roundNumber);\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, []);\n\n  //   /**\n  //    * Hide game rules\n  //    */\n  //   const hideRules = useCallback(async (roomId: string) => {\n  //     try {\n  //       await gameApi.hideRules(roomId);\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, []);\n\n  //   /**\n  //    * Set selected packet name\n  //    */\n  //   const setSelectedPacketName = useCallback(async (roomId: string, packetName: string) => {\n  //     try {\n  //       await gameApi.setSelectedPacketName(roomId, packetName);\n  //     } catch (error) {\n  //       throw error;\n  //     }\n  //   }, []);\n\n  //   /**\n  //    * Set current question (local state)\n  //    */\n  //   const setCurrentQuestionLocal = useCallback((question: Question | null) => {\n  //     dispatch(setCurrentQuestion(question));\n  //   }, [dispatch]);\n\n  //   /**\n  //    * Set questions (local state)\n  //    */\n  //   const setQuestionsLocal = useCallback((questions: Question[]) => {\n  //     dispatch(setQuestions(questions));\n  //   }, [dispatch]);\n\n  //   /**\n  //    * Set scores (local state)\n  //    */\n  //   const setScoresLocal = useCallback((scores: Score[]) => {\n  //     dispatch(setScores(scores));\n  //   }, [dispatch]);\n\n  //   /**\n  //    * Clear game errors\n  //    */\n  //   const clearGameError = useCallback(() => {\n  //     dispatch(clearError());\n  //   }, [dispatch]);\n\n  return {\n    //     // State\n    //     loading,\n    //     currentQuestion,\n    //     questions,\n    //     scores,\n    //     currentRound,\n    //     round2Grid,\n    //     round4Grid,\n\n    //     // API Actions\n    //     getQuestions,\n    //     getPrefetchQuestion,\n    //     getPacketNames,\n    //     sendGrid,\n    startRound,\n    //     submitPlayerAnswer,\n    broadcastAnswers,\n    sendCorrectAnswer\n    //     updateGameScoring,\n    //     updateTurn,\n    //     showRules,\n    //     hideRules,\n    //     setSelectedPacketName,\n\n    //     // Local State Actions\n    //     setCurrentQuestionLocal,\n    //     setQuestionsLocal,\n    //     setScoresLocal,\n    //     clearGameError,\n  };\n};\n_s(useGameApi, \"1u5wWuD42MeyaLutV7vI/TRRLLQ=\", false, function () {\n  return [useAppDispatch, useAppSelector];\n});\nexport default useGameApi;", "map": {"version": 3, "names": ["useCallback", "useAppDispatch", "useAppSelector", "gameApi", "useGameApi", "_s", "dispatch", "currentCorrectAnswer", "round2Grid", "round4Grid", "currentRound", "state", "game", "startRound", "roomId", "startRoundParams", "round", "grid", "undefined", "error", "startTimer", "broadcastAnswers", "sendCorrectAnswer", "answer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/shared/hooks/api/useGameApi.ts"], "sourcesContent": ["// // Game API hook\r\nimport { useCallback } from 'react';\r\nimport { useAppDispatch, useAppSelector } from '../../../app/store';\r\nimport {\r\n    submitAnswer,\r\n    updateScores,\r\n    setCurrentQuestion,\r\n    setQuestions,\r\n    setScores,\r\n    setRound2Grid,\r\n    setRound4Grid,\r\n    clearError\r\n} from '../../../app/store/slices/gameSlice';\r\nimport { gameApi } from '../../services/game/gameApi';\r\nimport {\r\n    GetQuestionsRequest,\r\n    SubmitAnswerRequest,\r\n    ScoringRequest,\r\n    SendGridRequest,\r\n    Question,\r\n    Score\r\n} from '../../types';\r\n\r\nexport const useGameApi = () => {\r\n    const dispatch = useAppDispatch();\r\n\r\n    const {\r\n        currentCorrectAnswer,\r\n        round2Grid,\r\n        round4Grid,\r\n        currentRound\r\n        // loading, \r\n        // currentQuestion, \r\n        // questions, \r\n        // scores, \r\n        // currentRound,\r\n        // round2Grid,\r\n        // round4Grid \r\n    } = useAppSelector(state => state.game);\r\n\r\n    /**\r\n     * Get questions for a round\r\n     */\r\n    //   const getQuestions = useCallback(async (params: GetQuestionsRequest) => {\r\n    //     try {\r\n    //       const result = await dispatch(fetchQuestions(params)).unwrap();\r\n    //       return result;\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, [dispatch]);\r\n\r\n    //   /**\r\n    //    * Get prefetch question\r\n    //    */\r\n    //   const getPrefetchQuestion = useCallback(async (params: { \r\n    //     testName: string; \r\n    //     round: number; \r\n    //     questionNumber: number \r\n    //   }) => {\r\n    //     try {\r\n    //       const question = await gameApi.getPrefetchQuestion(params);\r\n    //       return question;\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, []);\r\n\r\n    //   /**\r\n    //    * Get packet names\r\n    //    */\r\n    //   const getPacketNames = useCallback(async (testName: string) => {\r\n    //     try {\r\n    //       const packets = await gameApi.getPacketNames(testName);\r\n    //       return packets;\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, []);\r\n\r\n    //   /**\r\n    //    * Send grid to players\r\n    //    */\r\n    //   const sendGrid = useCallback(async (params: SendGridRequest) => {\r\n    //     try {\r\n    //       const success = await gameApi.sendGrid(params);\r\n\r\n    //       // Update local state based on current round\r\n    //       if (currentRound === 2) {\r\n    //         dispatch(setRound2Grid({\r\n    //           cells: params.grid,\r\n    //           rows: params.grid.length,\r\n    //           cols: params.grid[0]?.length || 0,\r\n    //           horizontalRows: [],\r\n    //           cnv: '',\r\n    //           selectedRows: [],\r\n    //           correctRows: [],\r\n    //           incorrectRows: [],\r\n    //         }));\r\n    //       } else if (currentRound === 4) {\r\n    //         // Convert grid to Round 4 format\r\n    //         const round4Cells = params.grid.map((row, rowIndex) =>\r\n    //           row.map((cell, colIndex) => ({\r\n    //             id: `${rowIndex}-${colIndex}`,\r\n    //             question: {} as Question, // Will be populated later\r\n    //             isSelected: false,\r\n    //             isAnswered: false,\r\n    //             difficulty: 'easy' as const,\r\n    //             points: 20,\r\n    //           }))\r\n    //         );\r\n\r\n    //         dispatch(setRound4Grid({\r\n    //           cells: round4Cells,\r\n    //           selectedDifficulties: [],\r\n    //           starPositions: [],\r\n    //         }));\r\n    //       }\r\n\r\n    //       return success;\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, [dispatch, currentRound]);\r\n\r\n    /**\r\n     * Start a new round\r\n     */\r\n    const startRound = useCallback(async (roomId: string) => {\r\n        try {\r\n            const startRoundParams = {\r\n                roomId: roomId,\r\n                round: currentRound,\r\n                grid: currentRound === \"2\" || currentRound === \"4\" ?\r\n                    currentRound == \"2\" ? round2Grid?.grid : round4Grid?.grid\r\n                    : undefined\r\n            }\r\n            await gameApi.startRound(startRoundParams);\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }, []);\r\n\r\n    /**\r\n     * Start a new round\r\n     */\r\n    const startTimer = useCallback(async (roomId: string) => {\r\n        try {\r\n            const startRoundParams = {\r\n                roomId: roomId,\r\n                round: currentRound,\r\n                grid: currentRound === \"2\" || currentRound === \"4\" ?\r\n                    currentRound == \"2\" ? round2Grid?.grid : round4Grid?.grid\r\n                    : undefined\r\n            }\r\n            await gameApi.startRound(startRoundParams);\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }, []);\r\n\r\n    //   /**\r\n    //    * Submit player answer\r\n    //    */\r\n    //   const submitPlayerAnswer = useCallback(async (params: SubmitAnswerRequest) => {\r\n    //     try {\r\n    //       const result = await dispatch(submitAnswer(params)).unwrap();\r\n    //       return result;\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, [dispatch]);\r\n\r\n    /**\r\n     * Broadcast player answers\r\n     */\r\n    const broadcastAnswers = useCallback(async (roomId: string) => {\r\n        try {\r\n            await gameApi.broadcastAnswers(roomId);\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }, []);\r\n\r\n\r\n    /**\r\n     * Broadcast player answers\r\n     */\r\n    const sendCorrectAnswer = useCallback(async (roomId: string) => {\r\n        try {\r\n            await gameApi.sendCorrectAnswer(\r\n                {\r\n                    answer: currentCorrectAnswer,\r\n                    roomId: roomId\r\n                }\r\n            );\r\n        } catch (error) {\r\n            throw error;\r\n        }\r\n    }, []);\r\n\r\n    //   /**\r\n    //    * Update game scoring\r\n    //    */\r\n    //   const updateGameScoring = useCallback(async (params: ScoringRequest) => {\r\n    //     try {\r\n    //       const result = await dispatch(updateScores(params)).unwrap();\r\n    //       return result;\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, [dispatch]);\r\n\r\n    //   /**\r\n    //    * Update current turn\r\n    //    */\r\n    //   const updateTurn = useCallback(async (roomId: string, turn: number) => {\r\n    //     try {\r\n    //       await gameApi.updateTurn(roomId, turn);\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, []);\r\n\r\n    //   /**\r\n    //    * Show game rules\r\n    //    */\r\n    //   const showRules = useCallback(async (roomId: string, roundNumber: string) => {\r\n    //     try {\r\n    //       await gameApi.showRules(roomId, roundNumber);\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, []);\r\n\r\n    //   /**\r\n    //    * Hide game rules\r\n    //    */\r\n    //   const hideRules = useCallback(async (roomId: string) => {\r\n    //     try {\r\n    //       await gameApi.hideRules(roomId);\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, []);\r\n\r\n    //   /**\r\n    //    * Set selected packet name\r\n    //    */\r\n    //   const setSelectedPacketName = useCallback(async (roomId: string, packetName: string) => {\r\n    //     try {\r\n    //       await gameApi.setSelectedPacketName(roomId, packetName);\r\n    //     } catch (error) {\r\n    //       throw error;\r\n    //     }\r\n    //   }, []);\r\n\r\n    //   /**\r\n    //    * Set current question (local state)\r\n    //    */\r\n    //   const setCurrentQuestionLocal = useCallback((question: Question | null) => {\r\n    //     dispatch(setCurrentQuestion(question));\r\n    //   }, [dispatch]);\r\n\r\n    //   /**\r\n    //    * Set questions (local state)\r\n    //    */\r\n    //   const setQuestionsLocal = useCallback((questions: Question[]) => {\r\n    //     dispatch(setQuestions(questions));\r\n    //   }, [dispatch]);\r\n\r\n    //   /**\r\n    //    * Set scores (local state)\r\n    //    */\r\n    //   const setScoresLocal = useCallback((scores: Score[]) => {\r\n    //     dispatch(setScores(scores));\r\n    //   }, [dispatch]);\r\n\r\n    //   /**\r\n    //    * Clear game errors\r\n    //    */\r\n    //   const clearGameError = useCallback(() => {\r\n    //     dispatch(clearError());\r\n    //   }, [dispatch]);\r\n\r\n    return {\r\n        //     // State\r\n        //     loading,\r\n        //     currentQuestion,\r\n        //     questions,\r\n        //     scores,\r\n        //     currentRound,\r\n        //     round2Grid,\r\n        //     round4Grid,\r\n\r\n        //     // API Actions\r\n        //     getQuestions,\r\n        //     getPrefetchQuestion,\r\n        //     getPacketNames,\r\n        //     sendGrid,\r\n        startRound,\r\n        //     submitPlayerAnswer,\r\n        broadcastAnswers,\r\n        sendCorrectAnswer,\r\n        //     updateGameScoring,\r\n        //     updateTurn,\r\n        //     showRules,\r\n        //     hideRules,\r\n        //     setSelectedPacketName,\r\n\r\n        //     // Local State Actions\r\n        //     setCurrentQuestionLocal,\r\n        //     setQuestionsLocal,\r\n        //     setScoresLocal,\r\n        //     clearGameError,\r\n    };\r\n};\r\n\r\nexport default useGameApi;\r\n"], "mappings": ";AAAA;AACA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,cAAc,EAAEC,cAAc,QAAQ,oBAAoB;AAWnE,SAASC,OAAO,QAAQ,6BAA6B;AAUrD,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGL,cAAc,CAAC,CAAC;EAEjC,MAAM;IACFM,oBAAoB;IACpBC,UAAU;IACVC,UAAU;IACVC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ,CAAC,GAAGR,cAAc,CAACS,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;;EAEvC;AACJ;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;AACJ;AACA;EACI,MAAMC,UAAU,GAAGb,WAAW,CAAC,MAAOc,MAAc,IAAK;IACrD,IAAI;MACA,MAAMC,gBAAgB,GAAG;QACrBD,MAAM,EAAEA,MAAM;QACdE,KAAK,EAAEN,YAAY;QACnBO,IAAI,EAAEP,YAAY,KAAK,GAAG,IAAIA,YAAY,KAAK,GAAG,GAC9CA,YAAY,IAAI,GAAG,GAAGF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,IAAI,GAAGR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,IAAI,GACvDC;MACV,CAAC;MACD,MAAMf,OAAO,CAACU,UAAU,CAACE,gBAAgB,CAAC;IAC9C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZ,MAAMA,KAAK;IACf;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;AACJ;AACA;EACI,MAAMC,UAAU,GAAGpB,WAAW,CAAC,MAAOc,MAAc,IAAK;IACrD,IAAI;MACA,MAAMC,gBAAgB,GAAG;QACrBD,MAAM,EAAEA,MAAM;QACdE,KAAK,EAAEN,YAAY;QACnBO,IAAI,EAAEP,YAAY,KAAK,GAAG,IAAIA,YAAY,KAAK,GAAG,GAC9CA,YAAY,IAAI,GAAG,GAAGF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,IAAI,GAAGR,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEQ,IAAI,GACvDC;MACV,CAAC;MACD,MAAMf,OAAO,CAACU,UAAU,CAACE,gBAAgB,CAAC;IAC9C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZ,MAAMA,KAAK;IACf;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;AACJ;AACA;EACI,MAAME,gBAAgB,GAAGrB,WAAW,CAAC,MAAOc,MAAc,IAAK;IAC3D,IAAI;MACA,MAAMX,OAAO,CAACkB,gBAAgB,CAACP,MAAM,CAAC;IAC1C,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZ,MAAMA,KAAK;IACf;EACJ,CAAC,EAAE,EAAE,CAAC;;EAGN;AACJ;AACA;EACI,MAAMG,iBAAiB,GAAGtB,WAAW,CAAC,MAAOc,MAAc,IAAK;IAC5D,IAAI;MACA,MAAMX,OAAO,CAACmB,iBAAiB,CAC3B;QACIC,MAAM,EAAEhB,oBAAoB;QAC5BO,MAAM,EAAEA;MACZ,CACJ,CAAC;IACL,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZ,MAAMA,KAAK;IACf;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA,OAAO;IACH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACAN,UAAU;IACV;IACAQ,gBAAgB;IAChBC;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;EACJ,CAAC;AACL,CAAC;AAACjB,EAAA,CArSWD,UAAU;EAAA,QACFH,cAAc,EAc3BC,cAAc;AAAA;AAwRtB,eAAeE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}