{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\HostAnswer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { useHost } from '../context/hostContext';\nimport { usePlayer } from '../context/playerContext';\nimport { updateScore } from '../pages/Host/Management/service';\nimport { listenToScores } from '../services/firebaseServices';\nimport { useSearchParams } from 'react-router-dom';\nimport { openBuzz } from './services';\nimport { setCurrrentTurnToPlayer } from '../layouts/services';\nimport { toast } from 'react-toastify';\nimport { useFirebaseListener } from '../shared/hooks';\nimport SimpleColorPicker from './SimpleColorPicker';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction HostAnswer() {\n  _s();\n  const [turn, setTurn] = useState(0);\n  const [mode, setMode] = useState(\"\");\n  const {\n    playersArray,\n    playerFlashes,\n    answerList,\n    setAnswerList,\n    level,\n    selectedTopic\n  } = usePlayer();\n  const {\n    handleScoreAdjust,\n    playerScores,\n    setPlayerScores,\n    handleNextQuestion,\n    numberOfSelectedRow,\n    playerColors,\n    setPlayerColors,\n    selectedPlayer,\n    setSelectedPlayer\n  } = useHost();\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n  const roomId = searchParams.get(\"roomId\") || \"1\";\n  const {\n    listenToBroadcastedAnswer,\n    listen\n  } = useFirebaseListener(roomId);\n\n  // Generate spots array based on number of players (up to 8)\n  const maxPlayers = playersArray ? Math.max(4, playersArray.length) : 4;\n  const spots = Array.from({\n    length: Math.min(maxPlayers, 8)\n  }, (_, i) => i + 1);\n  const isFirstMounted = useRef(true);\n  const isAnswerListFirstMounted = useRef(true);\n  // Initialize turn assignments based on max players\n  const initializeTurnAssignments = () => {\n    const assignments = {};\n    for (let i = 1; i <= Math.min(maxPlayers, 8); i++) {\n      assignments[i] = null;\n    }\n    return assignments;\n  };\n  const [turnAssignments, setTurnAssignments] = useState(initializeTurnAssignments());\n  const handleAssignTurn = (spot, turnNumber) => {\n    setTurnAssignments(prev => {\n      // Remove this turnNumber from any other spot\n      const updated = {\n        ...prev\n      };\n      Object.keys(updated).forEach(key => {\n        if (updated[Number(key)] === turnNumber) {\n          updated[Number(key)] = null;\n        }\n      });\n      updated[spot] = turnNumber;\n      return updated;\n    });\n  };\n  useEffect(() => {\n    setTurn(0);\n    setMode(localStorage.getItem(`mode_${roomId}`) || \"\");\n  }, [round]);\n  useEffect(() => {\n    const currentScoreList = localStorage.getItem(\"scoreList\");\n    if (currentScoreList) {\n      setPlayerScores(JSON.parse(currentScoreList));\n    }\n  }, [round]);\n  useEffect(() => {\n    const unsubscribeScores = listenToScores(roomId, data => {\n      if (isFirstMounted.current) {\n        isFirstMounted.current = false;\n        return;\n      }\n      setPlayerScores(data);\n    });\n    return () => unsubscribeScores();\n  }, [round, roomId]);\n  useEffect(() => {\n    const unsubscribeBroadcastedAnswer = listenToBroadcastedAnswer(roomId, answerList => {\n      if (isAnswerListFirstMounted.current) {\n        isAnswerListFirstMounted.current = false;\n        return;\n      }\n      console.log(\"answerList\", answerList);\n      setAnswerList(answerList);\n    });\n    return () => unsubscribeBroadcastedAnswer();\n  }, [roomId]);\n  const storedPlayers = localStorage.getItem(\"playerList\");\n  const playerList = playersArray || (storedPlayers ? JSON.parse(storedPlayers) : []);\n\n  // Handle color change\n  const handleColorChange = (playerStt, color) => {\n    const newColors = {\n      ...playerColors\n    };\n    if (color) {\n      newColors[playerStt] = color;\n    } else {\n      delete newColors[playerStt];\n    }\n    setPlayerColors(newColors);\n  };\n\n  // Get used colors\n  const usedColors = new Set(Object.values(playerColors));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex gap-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `grid ${spots.length > 4 ? 'grid-cols-4 grid-rows-2' : 'grid-cols-2'} gap-4 flex-1`,\n      children: spots.map(spotNumber => {\n        var _playerScore$score;\n        const player = playerList.find(p => parseInt(p.stt) === spotNumber);\n        const playerScore = playerScores.find(score => score.stt === spotNumber.toString());\n        const answer = answerList === null || answerList === void 0 ? void 0 : answerList.find(a => parseInt(a.stt) === spotNumber);\n        const isCurrent = turn !== null && Number(turn) === spotNumber;\n        return player ? /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: () => setSelectedPlayer(player),\n          className: `flex items-center w-full min-h-[180px] bg-slate-800/80 rounded-xl p-4 shadow-md border border-slate-700/50 transition-all duration-200 ${isCurrent ? \"ring-4 ring-yellow-400 border-yellow-400\" : \"\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mr-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: player.avatar,\n              alt: \"Player\",\n              className: \"w-16 h-16 rounded-full border-2 border-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 33\n            }, this), round === \"4\" && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-1 -right-1\",\n              children: /*#__PURE__*/_jsxDEV(SimpleColorPicker, {\n                playerStt: player.stt,\n                currentColor: playerColors[player.stt],\n                onColorChange: handleColorChange,\n                usedColors: usedColors\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white font-bold border-b border-slate-700/50 pb-1\",\n              children: `player_${player.stt}: ${player.userName}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white text-lg font-bold\",\n              children: (_playerScore$score = playerScore === null || playerScore === void 0 ? void 0 : playerScore.score) !== null && _playerScore$score !== void 0 ? _playerScore$score : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white border-b border-slate-700/50 pb-1 mt-1\",\n              children: (answer === null || answer === void 0 ? void 0 : answer.answer) || \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-sm mt-1\",\n              children: answer !== null && answer !== void 0 && answer.time ? `${answer.time}s` : \"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-300 text-xs\",\n                children: \"L\\u01B0\\u1EE3t:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 37\n              }, this), [1, 2, 3, 4].map(turnNum => /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: `px-2 py-1 rounded text-xs font-semibold border ${turnAssignments[spotNumber] === turnNum ? \"bg-blue-500 text-white border-blue-600\" : \"bg-slate-700 text-blue-200 border-slate-600 hover:bg-blue-600 hover:text-white\"}`,\n                onClick: e => {\n                  e.stopPropagation();\n                  handleAssignTurn(spotNumber, turnNum);\n                },\n                children: turnNum\n              }, turnNum, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 41\n              }, this)), turnAssignments[spotNumber] && /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"ml-1 px-2 py-1 rounded text-xs bg-gray-600 text-white border border-gray-700 hover:bg-gray-700\",\n                onClick: e => {\n                  e.stopPropagation();\n                  handleAssignTurn(spotNumber, null);\n                },\n                children: \"X\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 29\n          }, this)]\n        }, spotNumber, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-slate-800/80 rounded-xl min-h-[240px] opacity-50\"\n        }, spotNumber, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 25\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-80 flex flex-col gap-4\",\n      children: [selectedPlayer ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-slate-700 rounded-xl p-4 text-white shadow\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mx-auto w-16 h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: selectedPlayer.avatar,\n              alt: \"Selected\",\n              className: \"w-16 h-16 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 33\n            }, this), round === \"4\" && playerColors[selectedPlayer.stt] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-white shadow-lg\",\n              style: {\n                backgroundColor: playerColors[selectedPlayer.stt]\n              },\n              title: `Màu của ${selectedPlayer.userName}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center font-bold mt-2\",\n            children: selectedPlayer.userName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center\",\n            children: [\"Player_\", selectedPlayer.stt]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2 flex-wrap\",\n          children: [[5, 10].map(amount => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleScoreAdjust(parseInt(selectedPlayer.stt), amount, true, selectedPlayer.userName, selectedPlayer.avatar),\n            className: \"bg-green-500 hover:bg-green-600 text-white p-2 rounded shadow\",\n            children: [\"+\", amount]\n          }, `plus${amount}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 33\n          }, this)), [5, 10].map(amount => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleScoreAdjust(parseInt(selectedPlayer.stt), -amount, false, selectedPlayer.userName, selectedPlayer.avatar),\n            className: \"bg-red-500 hover:bg-red-600 text-white p-2 rounded shadow\",\n            children: [\"-\", amount]\n          }, `minus${amount}`, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 33\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 25\n        }, this), (round === \"3\" || round === \"4\" || round === \"2\" || round === \"turn\") && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setTurn(parseInt(selectedPlayer.stt));\n              setCurrrentTurnToPlayer(roomId, parseInt(selectedPlayer.stt));\n              toast.success(`Đã cập nhật lượt thi cho ${selectedPlayer.userName}`);\n            },\n            className: `w-full ${turn === parseInt(selectedPlayer.stt) ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-500 hover:bg-blue-600'} text-white py-2 rounded shadow`,\n            children: turn === parseInt(selectedPlayer.stt) ? 'Đang thi' : 'Cập nhật lượt thi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 33\n          }, this), round === \"2\" && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                console.log(\"numberOfSelectedRow\", numberOfSelectedRow);\n                const obstaclePoint = (7 - numberOfSelectedRow) * 15;\n                console.log(\"obstaclePoint\", obstaclePoint);\n                updateScore(roomId, [], \"auto\", \"2\", turn.toString(), \"true\", \"main\", \"\", \"\", \"\", \"\", \"true\", obstaclePoint);\n                toast.success(`Đã cộng ${obstaclePoint} đúng CNV cho ${selectedPlayer.userName}`);\n              },\n              className: \"bg-green-600 hover:bg-green-700 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"Ch\\u1EA5m \\u0111i\\u1EC3m \\u0111\\xFAng CNV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 41\n          }, this), round === \"4\" && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                updateScore(roomId, [], \"auto\", \"4\", turn.toString(), \"true\", \"main\", level);\n              },\n              className: \"bg-green-600 hover:bg-green-700 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"\\u0110\\xFAng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: async () => {\n                await openBuzz(roomId);\n                toast.success(`Đã mở bấm chuông`);\n              },\n              className: \"bg-red-600 hover:bg-red-700 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"Sai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                updateScore(roomId, [], \"auto\", \"4\", turn.toString(), \"true\", \"nshv\", level);\n              },\n              className: \"bg-green-500 hover:bg-green-600 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"NSHV \\u0110\\xFAng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: async () => {\n                updateScore(roomId, [], \"auto\", \"4\", turn.toString(), \"false\", \"nshv\", level);\n                await openBuzz(roomId);\n                toast.success(`Đã mở bấm chuông`);\n              },\n              className: \"bg-red-500 hover:bg-red-600 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"NSHV Sai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                updateScore(roomId, [], \"auto\", \"4\", \"\", \"\", \"take_turn\", level, \"true\", selectedPlayer.stt, turn.toString());\n              },\n              className: \"bg-green-400 hover:bg-green-500 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"Gi\\xE0nh l\\u01B0\\u1EE3t \\u0110\\xFAng\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                updateScore(roomId, [], \"auto\", \"4\", \"\", \"\", \"take_turn\", level, \"false\", selectedPlayer.stt, turn.toString());\n              },\n              className: \"bg-red-400 hover:bg-red-500 text-white flex-1 min-w-[120px] rounded py-2\",\n              children: \"Gi\\xE0nh l\\u01B0\\u1EE3t Sai\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true) : round !== \"1\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-white text-center\",\n        children: \"Ch\\u1ECDn 1 ng\\u01B0\\u1EDDi ch\\u01A1i \\u0111\\u1EC3 \\u0111i\\u1EC1u khi\\u1EC3n\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 21\n      }, this) : null, /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          toast.success('Đã cập nhật điểm!');\n          updateScore(roomId, playerScores, \"manual\", round);\n          const newScoreList = [...playerScores].map(s => ({\n            ...s,\n            isCorrect: false,\n            isModified: false\n          }));\n          setPlayerScores(newScoreList);\n        },\n        className: \"bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white py-3 rounded-xl shadow font-semibold\",\n        children: \"X\\xE1c nh\\u1EADn \\u0111i\\u1EC3m\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          updateScore(roomId, playerScores, mode, round);\n          toast.success(`Đã cập nhật điểm cho vòng thi ${round}`);\n          // const newScoreList = [...playerScores].map((s) => ({\n          //     ...s,\n          //     isCorrect: false,\n          //     isModified: false,\n          // }));\n          // setPlayerScores(newScoreList);\n        },\n        className: \"bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white py-3 rounded-xl shadow font-semibold\",\n        children: \"Ch\\u1EA5m \\u0111i\\u1EC3m t\\u1EF1 \\u0111\\u1ED9ng\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 9\n  }, this);\n}\n_s(HostAnswer, \"FfaY6J+QoRZdI7abXO9x1J7WFcI=\", false, function () {\n  return [usePlayer, useHost, useSearchParams, useFirebaseListener];\n});\n_c = HostAnswer;\nexport default HostAnswer;\nvar _c;\n$RefreshReg$(_c, \"HostAnswer\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useHost", "usePlayer", "updateScore", "listenToScores", "useSearchParams", "openBuzz", "setCurrrentTurnToPlayer", "toast", "useFirebaseListener", "SimpleColorPicker", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HostAnswer", "_s", "turn", "setTurn", "mode", "setMode", "players<PERSON><PERSON>y", "playerFlashes", "answerList", "setAnswerList", "level", "selectedTopic", "handleScoreAdjust", "playerScores", "setPlayerScores", "handleNextQuestion", "numberOfSelectedRow", "playerColors", "setPlayerColors", "selectedPlayer", "setSelectedPlayer", "searchParams", "round", "get", "roomId", "listenToBroadcastedAnswer", "listen", "maxPlayers", "Math", "max", "length", "spots", "Array", "from", "min", "_", "i", "isFirstMounted", "isAnswerListFirstMounted", "initializeTurnAssignments", "assignments", "turnAssignments", "setTurnAssignments", "handleAssignTurn", "spot", "turnNumber", "prev", "updated", "Object", "keys", "for<PERSON>ach", "key", "Number", "localStorage", "getItem", "currentScoreList", "JSON", "parse", "unsubscribeScores", "data", "current", "unsubscribeBroadcastedAnswer", "console", "log", "storedPlayers", "playerList", "handleColorChange", "player<PERSON>tt", "color", "newColors", "usedColors", "Set", "values", "className", "children", "map", "spotNumber", "_playerScore$score", "player", "find", "p", "parseInt", "stt", "playerScore", "score", "toString", "answer", "a", "isCurrent", "onClick", "src", "avatar", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentColor", "onColorChange", "userName", "time", "turnNum", "type", "e", "stopPropagation", "style", "backgroundColor", "title", "amount", "success", "obstaclePoint", "newScoreList", "s", "isCorrect", "isModified", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/HostAnswer.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\r\nimport { useHost } from '../context/hostContext';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { User } from '../type';\r\nimport { updateScore } from '../pages/Host/Management/service';\r\nimport { listenToBroadcastedAnswer, listenToScores } from '../services/firebaseServices';\r\nimport { Answer, Score } from '../type';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { openBuzz } from './services';\r\nimport { setCurrrentTurnToPlayer } from '../layouts/services';\r\nimport { toast } from 'react-toastify';\r\nimport { useFirebaseListener } from '../shared/hooks';\r\nimport SimpleColorPicker from './SimpleColorPicker';\r\n\r\n\r\nfunction HostAnswer() {\r\n    const [turn, setTurn] = useState<number>(0);\r\n    const [mode, setMode] = useState<string>(\"\")\r\n    const { playersArray, playerFlashes, answerList, setAnswerList, level, selectedTopic } = usePlayer();\r\n    const { handleScoreAdjust, playerScores, setPlayerScores, handleNextQuestion, numberOfSelectedRow, playerColors, setPlayerColors, selectedPlayer, setSelectedPlayer } = useHost();\r\n    const [searchParams] = useSearchParams();\r\n    const round = searchParams.get(\"round\") || \"1\";\r\n    const roomId = searchParams.get(\"roomId\") || \"1\";\r\n\r\n    const {listenToBroadcastedAnswer, listen} = useFirebaseListener(roomId)\r\n\r\n    // Generate spots array based on number of players (up to 8)\r\n    const maxPlayers = playersArray ? Math.max(4, playersArray.length) : 4;\r\n    const spots = Array.from({ length: Math.min(maxPlayers, 8) }, (_, i) => i + 1);\r\n    const isFirstMounted = useRef(true)\r\n    const isAnswerListFirstMounted = useRef(true)\r\n    // Initialize turn assignments based on max players\r\n    const initializeTurnAssignments = () => {\r\n        const assignments: { [spot: number]: number | null } = {};\r\n        for (let i = 1; i <= Math.min(maxPlayers, 8); i++) {\r\n            assignments[i] = null;\r\n        }\r\n        return assignments;\r\n    };\r\n    const [turnAssignments, setTurnAssignments] = useState<{ [spot: number]: number | null }>(initializeTurnAssignments());\r\n\r\n    const handleAssignTurn = (spot: number, turnNumber: number) => {\r\n        setTurnAssignments((prev) => {\r\n            // Remove this turnNumber from any other spot\r\n            const updated = { ...prev };\r\n            Object.keys(updated).forEach((key) => {\r\n                if (updated[Number(key)] === turnNumber) {\r\n                    updated[Number(key)] = null;\r\n                }\r\n            });\r\n            updated[spot] = turnNumber;\r\n            return updated;\r\n        });\r\n    };\r\n\r\n    useEffect(() => {\r\n        setTurn(0);\r\n        setMode(localStorage.getItem(`mode_${roomId}`) || \"\")\r\n    }, [round]);\r\n\r\n\r\n    useEffect(() => {\r\n        const currentScoreList = localStorage.getItem(\"scoreList\");\r\n        if (currentScoreList) {\r\n            setPlayerScores(JSON.parse(currentScoreList));\r\n        }\r\n    }, [round]);\r\n\r\n    useEffect(() => {\r\n        const unsubscribeScores = listenToScores(roomId, (data) => {\r\n            if (isFirstMounted.current) {\r\n                isFirstMounted.current = false\r\n                return\r\n            }\r\n            setPlayerScores(data);\r\n        });\r\n        return () => unsubscribeScores();\r\n    }, [round, roomId]);\r\n\r\n    useEffect(() => {\r\n        const unsubscribeBroadcastedAnswer = listenToBroadcastedAnswer(roomId, (answerList) => {\r\n            if (isAnswerListFirstMounted.current) {\r\n                isAnswerListFirstMounted.current = false\r\n                return\r\n            }\r\n\r\n            console.log(\"answerList\", answerList);\r\n\r\n            setAnswerList(answerList);\r\n        });\r\n        return () => unsubscribeBroadcastedAnswer();\r\n    }, [roomId]);\r\n\r\n    const storedPlayers = localStorage.getItem(\"playerList\");\r\n    const playerList = playersArray || (storedPlayers ? JSON.parse(storedPlayers) : []);\r\n\r\n    // Handle color change\r\n    const handleColorChange = (playerStt: string, color: string) => {\r\n        const newColors = { ...playerColors };\r\n        if (color) {\r\n            newColors[playerStt] = color;\r\n        } else {\r\n            delete newColors[playerStt];\r\n        }\r\n        setPlayerColors(newColors);\r\n    };\r\n\r\n    // Get used colors\r\n    const usedColors = new Set<string>(Object.values(playerColors));\r\n\r\n    return (\r\n        <div className=\"flex gap-6\">\r\n            {/* Left: Player grid */}\r\n            <div className={`grid ${spots.length > 4 ? 'grid-cols-4 grid-rows-2' : 'grid-cols-2'} gap-4 flex-1`}>\r\n                {spots.map((spotNumber) => {\r\n                    const player = playerList.find((p: User) => parseInt(p.stt) === spotNumber);\r\n                    const playerScore = playerScores.find((score: Score) => score.stt === spotNumber.toString());\r\n                    const answer = answerList?.find((a: Answer) => parseInt(a.stt) === spotNumber);\r\n                    const isCurrent = turn !== null && Number(turn) === spotNumber;\r\n                    return player ? (\r\n                        <div\r\n                            key={spotNumber}\r\n                            onClick={() => setSelectedPlayer(player)}\r\n                            className={`flex items-center w-full min-h-[180px] bg-slate-800/80 rounded-xl p-4 shadow-md border border-slate-700/50 transition-all duration-200 ${isCurrent ? \"ring-4 ring-yellow-400 border-yellow-400\" : \"\"}`}\r\n                        >\r\n                            <div className=\"relative mr-4\">\r\n                                <img\r\n                                    src={player.avatar}\r\n                                    alt=\"Player\"\r\n                                    className=\"w-16 h-16 rounded-full border-2 border-white\"\r\n                                />\r\n                                {/* Color picker for Round 4 */}\r\n                                {round === \"4\" && (\r\n                                    <div className=\"absolute -bottom-1 -right-1\">\r\n                                        <SimpleColorPicker\r\n                                            playerStt={player.stt}\r\n                                            currentColor={playerColors[player.stt]}\r\n                                            onColorChange={handleColorChange}\r\n                                            usedColors={usedColors}\r\n                                        />\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                            <div className=\"flex flex-col flex-1\">\r\n                                <p className=\"text-white font-bold border-b border-slate-700/50 pb-1\">\r\n                                    {`player_${player.stt}: ${player.userName}`}\r\n                                </p>\r\n                                <p className=\"text-white text-lg font-bold\">{playerScore?.score ?? 0}</p>\r\n                                <p className=\"text-white border-b border-slate-700/50 pb-1 mt-1\">\r\n                                    {answer?.answer || \"\"}\r\n                                </p>\r\n                                <p className=\"text-gray-400 text-sm mt-1\">\r\n                                    {answer?.time ? `${answer.time}s` : \"\"}\r\n                                </p>\r\n                                {/* Turn assignment UI */}\r\n                                <div className=\"mt-2 flex items-center gap-2\">\r\n                                    <span className=\"text-blue-300 text-xs\">Lượt:</span>\r\n                                    {[1, 2, 3, 4].map((turnNum) => (\r\n                                        <button\r\n                                            key={turnNum}\r\n                                            type=\"button\"\r\n                                            className={`px-2 py-1 rounded text-xs font-semibold border ${turnAssignments[spotNumber] === turnNum\r\n                                                    ? \"bg-blue-500 text-white border-blue-600\"\r\n                                                    : \"bg-slate-700 text-blue-200 border-slate-600 hover:bg-blue-600 hover:text-white\"\r\n                                                }`}\r\n                                            onClick={(e) => {\r\n                                                e.stopPropagation();\r\n                                                handleAssignTurn(spotNumber, turnNum);\r\n                                            }}\r\n                                        >\r\n                                            {turnNum}\r\n                                        </button>\r\n                                    ))}\r\n                                    {turnAssignments[spotNumber] && (\r\n                                        <button\r\n                                            type=\"button\"\r\n                                            className=\"ml-1 px-2 py-1 rounded text-xs bg-gray-600 text-white border border-gray-700 hover:bg-gray-700\"\r\n                                            onClick={(e) => {\r\n                                                e.stopPropagation();\r\n                                                handleAssignTurn(spotNumber, null as any);\r\n                                            }}\r\n                                        >\r\n                                            X\r\n                                        </button>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    ) : (\r\n                        <div key={spotNumber} className=\"bg-slate-800/80 rounded-xl min-h-[240px] opacity-50\" />\r\n                    );\r\n                })}\r\n            </div>\r\n\r\n            {/* Right: Selected player controls */}\r\n            <div className=\"w-80 flex flex-col gap-4\">\r\n                {selectedPlayer ? (\r\n                    <>\r\n                        <div className=\"bg-slate-700 rounded-xl p-4 text-white shadow\">\r\n                            <div className=\"relative mx-auto w-16 h-16\">\r\n                                <img src={selectedPlayer.avatar} alt=\"Selected\" className=\"w-16 h-16 rounded-full\" />\r\n                                {/* Color indicator for Round 4 */}\r\n                                {round === \"4\" && playerColors[selectedPlayer.stt] && (\r\n                                    <div\r\n                                        className=\"absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-white shadow-lg\"\r\n                                        style={{ backgroundColor: playerColors[selectedPlayer.stt] }}\r\n                                        title={`Màu của ${selectedPlayer.userName}`}\r\n                                    ></div>\r\n                                )}\r\n                            </div>\r\n                            <p className=\"text-center font-bold mt-2\">{selectedPlayer.userName}</p>\r\n                            <p className=\"text-center\">Player_{selectedPlayer.stt}</p>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2 flex-wrap\">\r\n                            {[5, 10].map((amount) => (\r\n                                <button\r\n                                    key={`plus${amount}`}\r\n                                    onClick={() => handleScoreAdjust(parseInt(selectedPlayer.stt), amount, true, selectedPlayer.userName, selectedPlayer.avatar)}\r\n                                    className=\"bg-green-500 hover:bg-green-600 text-white p-2 rounded shadow\"\r\n                                >\r\n                                    +{amount}\r\n                                </button>\r\n                            ))}\r\n                            {[5, 10].map((amount) => (\r\n                                <button\r\n                                    key={`minus${amount}`}\r\n                                    onClick={() => handleScoreAdjust(parseInt(selectedPlayer.stt), -amount, false, selectedPlayer.userName, selectedPlayer.avatar)}\r\n                                    className=\"bg-red-500 hover:bg-red-600 text-white p-2 rounded shadow\"\r\n                                >\r\n                                    -{amount}\r\n                                </button>\r\n                            ))}\r\n                        </div>\r\n\r\n                        {(round === \"3\" || round === \"4\" || round === \"2\" || round === \"turn\") && (\r\n                            <>\r\n                                <button\r\n                                    onClick={() => {\r\n                                        setTurn(parseInt(selectedPlayer.stt))\r\n                                        setCurrrentTurnToPlayer(roomId, parseInt(selectedPlayer.stt));\r\n                                        toast.success(`Đã cập nhật lượt thi cho ${selectedPlayer.userName}`);\r\n                                    }}\r\n                                    className={`w-full ${turn === parseInt(selectedPlayer.stt) ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-500 hover:bg-blue-600'} text-white py-2 rounded shadow`}\r\n                                >\r\n                                    {turn === parseInt(selectedPlayer.stt) ? 'Đang thi' : 'Cập nhật lượt thi'}\r\n                                </button>\r\n\r\n                                {\r\n                                    round === \"2\" && (\r\n                                        <div className=\"flex flex-wrap gap-2\">\r\n                                            <button\r\n                                                onClick={() => {\r\n                                                    console.log(\"numberOfSelectedRow\", numberOfSelectedRow);\r\n\r\n                                                    const obstaclePoint = (7 - numberOfSelectedRow) * 15\r\n                                                    console.log(\"obstaclePoint\", obstaclePoint);\r\n\r\n                                                    updateScore(roomId, [], \"auto\", \"2\", turn.toString(), \"true\", \"main\", \"\", \"\", \"\", \"\", \"true\", obstaclePoint)\r\n                                                    toast.success(`Đã cộng ${obstaclePoint} đúng CNV cho ${selectedPlayer.userName}`);\r\n                                                }}\r\n                                                className=\"bg-green-600 hover:bg-green-700 text-white flex-1 min-w-[120px] rounded py-2\">\r\n                                                Chấm điểm đúng CNV\r\n                                            </button>\r\n                                        </div>\r\n                                    )\r\n                                }\r\n                                {/* Round 3 buttons moved to Round3.tsx for better UX */}\r\n                                {round === \"4\" && (\r\n                                    <div className=\"flex flex-wrap gap-2\">\r\n                                        <button\r\n                                            onClick={() => {\r\n                                                updateScore(roomId, [], \"auto\", \"4\", turn.toString(), \"true\", \"main\", level)\r\n                                            }}\r\n                                            className=\"bg-green-600 hover:bg-green-700 text-white flex-1 min-w-[120px] rounded py-2\">\r\n                                            Đúng\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={async () => {\r\n                                                await openBuzz(roomId)\r\n                                                toast.success(`Đã mở bấm chuông`);\r\n                                            }}\r\n                                            className=\"bg-red-600 hover:bg-red-700 text-white flex-1 min-w-[120px] rounded py-2\"\r\n                                        >\r\n                                            Sai\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => {\r\n                                                updateScore(roomId, [], \"auto\", \"4\", turn.toString(), \"true\", \"nshv\", level)\r\n                                            }}\r\n                                            className=\"bg-green-500 hover:bg-green-600 text-white flex-1 min-w-[120px] rounded py-2\"\r\n                                        >\r\n                                            NSHV Đúng\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={async () => {\r\n                                                updateScore(roomId, [], \"auto\", \"4\", turn.toString(), \"false\", \"nshv\", level)\r\n                                                await openBuzz(roomId)\r\n                                                toast.success(`Đã mở bấm chuông`);\r\n                                            }}\r\n                                            className=\"bg-red-500 hover:bg-red-600 text-white flex-1 min-w-[120px] rounded py-2\"\r\n                                        >\r\n                                            NSHV Sai\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => {\r\n                                                updateScore(roomId, [], \"auto\", \"4\", \"\", \"\", \"take_turn\", level, \"true\", selectedPlayer.stt, turn.toString())\r\n                                            }}\r\n                                            className=\"bg-green-400 hover:bg-green-500 text-white flex-1 min-w-[120px] rounded py-2\"\r\n                                        >\r\n                                            Giành lượt Đúng\r\n                                        </button>\r\n                                        <button\r\n                                            onClick={() => {\r\n                                                updateScore(roomId, [], \"auto\", \"4\", \"\", \"\", \"take_turn\", level, \"false\", selectedPlayer.stt, turn.toString())\r\n                                            }}\r\n                                            className=\"bg-red-400 hover:bg-red-500 text-white flex-1 min-w-[120px] rounded py-2\"\r\n                                        >\r\n                                            Giành lượt Sai\r\n                                        </button>\r\n                                    </div>\r\n                                )}\r\n                            </>\r\n                        )}\r\n                    </>\r\n                ) : (\r\n\r\n                   round !== \"1\" ? (\r\n                    <p className=\"text-white text-center\">Chọn 1 người chơi để điều khiển</p>\r\n                   ) : null\r\n                    \r\n                )}\r\n\r\n                {/* Confirm button */}\r\n                <button\r\n                    onClick={() => {\r\n                        toast.success('Đã cập nhật điểm!');\r\n                        updateScore(roomId, playerScores, \"manual\", round);\r\n                        const newScoreList = [...playerScores].map((s) => ({\r\n                            ...s,\r\n                            isCorrect: false,\r\n                            isModified: false,\r\n                        }));\r\n                        setPlayerScores(newScoreList);\r\n                    }}\r\n                    className=\"bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white py-3 rounded-xl shadow font-semibold\"\r\n                >\r\n                    Xác nhận điểm\r\n                </button>\r\n\r\n                <button\r\n                    onClick={() => {\r\n                        updateScore(roomId, playerScores, mode, round);\r\n                        toast.success(`Đã cập nhật điểm cho vòng thi ${round}`);\r\n                        // const newScoreList = [...playerScores].map((s) => ({\r\n                        //     ...s,\r\n                        //     isCorrect: false,\r\n                        //     isModified: false,\r\n                        // }));\r\n                        // setPlayerScores(newScoreList);\r\n                    }}\r\n                    className=\"bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white py-3 rounded-xl shadow font-semibold\"\r\n                >\r\n                    Chấm điểm tự động\r\n                </button>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default HostAnswer;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAAoCC,cAAc,QAAQ,8BAA8B;AAExF,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGpD,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAS,CAAC,CAAC;EAC3C,MAAM,CAACmB,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAS,EAAE,CAAC;EAC5C,MAAM;IAAEqB,YAAY;IAAEC,aAAa;IAAEC,UAAU;IAAEC,aAAa;IAAEC,KAAK;IAAEC;EAAc,CAAC,GAAGxB,SAAS,CAAC,CAAC;EACpG,MAAM;IAAEyB,iBAAiB;IAAEC,YAAY;IAAEC,eAAe;IAAEC,kBAAkB;IAAEC,mBAAmB;IAAEC,YAAY;IAAEC,eAAe;IAAEC,cAAc;IAAEC;EAAkB,CAAC,GAAGlC,OAAO,CAAC,CAAC;EACjL,MAAM,CAACmC,YAAY,CAAC,GAAG/B,eAAe,CAAC,CAAC;EACxC,MAAMgC,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EAC9C,MAAMC,MAAM,GAAGH,YAAY,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG;EAEhD,MAAM;IAACE,yBAAyB;IAAEC;EAAM,CAAC,GAAGhC,mBAAmB,CAAC8B,MAAM,CAAC;;EAEvE;EACA,MAAMG,UAAU,GAAGrB,YAAY,GAAGsB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEvB,YAAY,CAACwB,MAAM,CAAC,GAAG,CAAC;EACtE,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEH,MAAM,EAAEF,IAAI,CAACM,GAAG,CAACP,UAAU,EAAE,CAAC;EAAE,CAAC,EAAE,CAACQ,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAC9E,MAAMC,cAAc,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsD,wBAAwB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EAC7C;EACA,MAAMuD,yBAAyB,GAAGA,CAAA,KAAM;IACpC,MAAMC,WAA8C,GAAG,CAAC,CAAC;IACzD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIR,IAAI,CAACM,GAAG,CAACP,UAAU,EAAE,CAAC,CAAC,EAAES,CAAC,EAAE,EAAE;MAC/CI,WAAW,CAACJ,CAAC,CAAC,GAAG,IAAI;IACzB;IACA,OAAOI,WAAW;EACtB,CAAC;EACD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAoCsD,yBAAyB,CAAC,CAAC,CAAC;EAEtH,MAAMI,gBAAgB,GAAGA,CAACC,IAAY,EAAEC,UAAkB,KAAK;IAC3DH,kBAAkB,CAAEI,IAAI,IAAK;MACzB;MACA,MAAMC,OAAO,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC3BE,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAAEC,GAAG,IAAK;QAClC,IAAIJ,OAAO,CAACK,MAAM,CAACD,GAAG,CAAC,CAAC,KAAKN,UAAU,EAAE;UACrCE,OAAO,CAACK,MAAM,CAACD,GAAG,CAAC,CAAC,GAAG,IAAI;QAC/B;MACJ,CAAC,CAAC;MACFJ,OAAO,CAACH,IAAI,CAAC,GAAGC,UAAU;MAC1B,OAAOE,OAAO;IAClB,CAAC,CAAC;EACN,CAAC;EAEDhE,SAAS,CAAC,MAAM;IACZoB,OAAO,CAAC,CAAC,CAAC;IACVE,OAAO,CAACgD,YAAY,CAACC,OAAO,CAAC,QAAQ9B,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;EACzD,CAAC,EAAE,CAACF,KAAK,CAAC,CAAC;EAGXvC,SAAS,CAAC,MAAM;IACZ,MAAMwE,gBAAgB,GAAGF,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC1D,IAAIC,gBAAgB,EAAE;MAClBzC,eAAe,CAAC0C,IAAI,CAACC,KAAK,CAACF,gBAAgB,CAAC,CAAC;IACjD;EACJ,CAAC,EAAE,CAACjC,KAAK,CAAC,CAAC;EAEXvC,SAAS,CAAC,MAAM;IACZ,MAAM2E,iBAAiB,GAAGrE,cAAc,CAACmC,MAAM,EAAGmC,IAAI,IAAK;MACvD,IAAItB,cAAc,CAACuB,OAAO,EAAE;QACxBvB,cAAc,CAACuB,OAAO,GAAG,KAAK;QAC9B;MACJ;MACA9C,eAAe,CAAC6C,IAAI,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,MAAMD,iBAAiB,CAAC,CAAC;EACpC,CAAC,EAAE,CAACpC,KAAK,EAAEE,MAAM,CAAC,CAAC;EAEnBzC,SAAS,CAAC,MAAM;IACZ,MAAM8E,4BAA4B,GAAGpC,yBAAyB,CAACD,MAAM,EAAGhB,UAAU,IAAK;MACnF,IAAI8B,wBAAwB,CAACsB,OAAO,EAAE;QAClCtB,wBAAwB,CAACsB,OAAO,GAAG,KAAK;QACxC;MACJ;MAEAE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEvD,UAAU,CAAC;MAErCC,aAAa,CAACD,UAAU,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO,MAAMqD,4BAA4B,CAAC,CAAC;EAC/C,CAAC,EAAE,CAACrC,MAAM,CAAC,CAAC;EAEZ,MAAMwC,aAAa,GAAGX,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EACxD,MAAMW,UAAU,GAAG3D,YAAY,KAAK0D,aAAa,GAAGR,IAAI,CAACC,KAAK,CAACO,aAAa,CAAC,GAAG,EAAE,CAAC;;EAEnF;EACA,MAAME,iBAAiB,GAAGA,CAACC,SAAiB,EAAEC,KAAa,KAAK;IAC5D,MAAMC,SAAS,GAAG;MAAE,GAAGpD;IAAa,CAAC;IACrC,IAAImD,KAAK,EAAE;MACPC,SAAS,CAACF,SAAS,CAAC,GAAGC,KAAK;IAChC,CAAC,MAAM;MACH,OAAOC,SAAS,CAACF,SAAS,CAAC;IAC/B;IACAjD,eAAe,CAACmD,SAAS,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAASvB,MAAM,CAACwB,MAAM,CAACvD,YAAY,CAAC,CAAC;EAE/D,oBACIpB,OAAA;IAAK4E,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEvB7E,OAAA;MAAK4E,SAAS,EAAE,QAAQ1C,KAAK,CAACD,MAAM,GAAG,CAAC,GAAG,yBAAyB,GAAG,aAAa,eAAgB;MAAA4C,QAAA,EAC/F3C,KAAK,CAAC4C,GAAG,CAAEC,UAAU,IAAK;QAAA,IAAAC,kBAAA;QACvB,MAAMC,MAAM,GAAGb,UAAU,CAACc,IAAI,CAAEC,CAAO,IAAKC,QAAQ,CAACD,CAAC,CAACE,GAAG,CAAC,KAAKN,UAAU,CAAC;QAC3E,MAAMO,WAAW,GAAGtE,YAAY,CAACkE,IAAI,CAAEK,KAAY,IAAKA,KAAK,CAACF,GAAG,KAAKN,UAAU,CAACS,QAAQ,CAAC,CAAC,CAAC;QAC5F,MAAMC,MAAM,GAAG9E,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuE,IAAI,CAAEQ,CAAS,IAAKN,QAAQ,CAACM,CAAC,CAACL,GAAG,CAAC,KAAKN,UAAU,CAAC;QAC9E,MAAMY,SAAS,GAAGtF,IAAI,KAAK,IAAI,IAAIkD,MAAM,CAAClD,IAAI,CAAC,KAAK0E,UAAU;QAC9D,OAAOE,MAAM,gBACTjF,OAAA;UAEI4F,OAAO,EAAEA,CAAA,KAAMrE,iBAAiB,CAAC0D,MAAM,CAAE;UACzCL,SAAS,EAAE,0IAA0Ie,SAAS,GAAG,0CAA0C,GAAG,EAAE,EAAG;UAAAd,QAAA,gBAEnN7E,OAAA;YAAK4E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B7E,OAAA;cACI6F,GAAG,EAAEZ,MAAM,CAACa,MAAO;cACnBC,GAAG,EAAC,QAAQ;cACZnB,SAAS,EAAC;YAA8C;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,EAED1E,KAAK,KAAK,GAAG,iBACVzB,OAAA;cAAK4E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACxC7E,OAAA,CAACF,iBAAiB;gBACdwE,SAAS,EAAEW,MAAM,CAACI,GAAI;gBACtBe,YAAY,EAAEhF,YAAY,CAAC6D,MAAM,CAACI,GAAG,CAAE;gBACvCgB,aAAa,EAAEhC,iBAAkB;gBACjCI,UAAU,EAAEA;cAAW;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACNnG,OAAA;YAAK4E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC7E,OAAA;cAAG4E,SAAS,EAAC,wDAAwD;cAAAC,QAAA,EAChE,UAAUI,MAAM,CAACI,GAAG,KAAKJ,MAAM,CAACqB,QAAQ;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACJnG,OAAA;cAAG4E,SAAS,EAAC,8BAA8B;cAAAC,QAAA,GAAAG,kBAAA,GAAEM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,KAAK,cAAAP,kBAAA,cAAAA,kBAAA,GAAI;YAAC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEnG,OAAA;cAAG4E,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC3D,CAAAY,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEA,MAAM,KAAI;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJnG,OAAA;cAAG4E,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACpCY,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEc,IAAI,GAAG,GAAGd,MAAM,CAACc,IAAI,GAAG,GAAG;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAEJnG,OAAA;cAAK4E,SAAS,EAAC,8BAA8B;cAAAC,QAAA,gBACzC7E,OAAA;gBAAM4E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACnD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAAE0B,OAAO,iBACtBxG,OAAA;gBAEIyG,IAAI,EAAC,QAAQ;gBACb7B,SAAS,EAAE,kDAAkDhC,eAAe,CAACmC,UAAU,CAAC,KAAKyB,OAAO,GAC1F,wCAAwC,GACxC,gFAAgF,EACnF;gBACPZ,OAAO,EAAGc,CAAC,IAAK;kBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnB7D,gBAAgB,CAACiC,UAAU,EAAEyB,OAAO,CAAC;gBACzC,CAAE;gBAAA3B,QAAA,EAED2B;cAAO,GAXHA,OAAO;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYR,CACX,CAAC,EACDvD,eAAe,CAACmC,UAAU,CAAC,iBACxB/E,OAAA;gBACIyG,IAAI,EAAC,QAAQ;gBACb7B,SAAS,EAAC,gGAAgG;gBAC1GgB,OAAO,EAAGc,CAAC,IAAK;kBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnB7D,gBAAgB,CAACiC,UAAU,EAAE,IAAW,CAAC;gBAC7C,CAAE;gBAAAF,QAAA,EACL;cAED;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GAjEDpB,UAAU;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkEd,CAAC,gBAENnG,OAAA;UAAsB4E,SAAS,EAAC;QAAqD,GAA3EG,UAAU;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmE,CAC1F;MACL,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnG,OAAA;MAAK4E,SAAS,EAAC,0BAA0B;MAAAC,QAAA,GACpCvD,cAAc,gBACXtB,OAAA,CAAAE,SAAA;QAAA2E,QAAA,gBACI7E,OAAA;UAAK4E,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC1D7E,OAAA;YAAK4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACvC7E,OAAA;cAAK6F,GAAG,EAAEvE,cAAc,CAACwE,MAAO;cAACC,GAAG,EAAC,UAAU;cAACnB,SAAS,EAAC;YAAwB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAEpF1E,KAAK,KAAK,GAAG,IAAIL,YAAY,CAACE,cAAc,CAAC+D,GAAG,CAAC,iBAC9CrF,OAAA;cACI4E,SAAS,EAAC,kFAAkF;cAC5FgC,KAAK,EAAE;gBAAEC,eAAe,EAAEzF,YAAY,CAACE,cAAc,CAAC+D,GAAG;cAAE,CAAE;cAC7DyB,KAAK,EAAE,WAAWxF,cAAc,CAACgF,QAAQ;YAAG;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACNnG,OAAA;YAAG4E,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAEvD,cAAc,CAACgF;UAAQ;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvEnG,OAAA;YAAG4E,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,SAAO,EAACvD,cAAc,CAAC+D,GAAG;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAENnG,OAAA;UAAK4E,SAAS,EAAC,sBAAsB;UAAAC,QAAA,GAChC,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAEiC,MAAM,iBAChB/G,OAAA;YAEI4F,OAAO,EAAEA,CAAA,KAAM7E,iBAAiB,CAACqE,QAAQ,CAAC9D,cAAc,CAAC+D,GAAG,CAAC,EAAE0B,MAAM,EAAE,IAAI,EAAEzF,cAAc,CAACgF,QAAQ,EAAEhF,cAAc,CAACwE,MAAM,CAAE;YAC7HlB,SAAS,EAAC,+DAA+D;YAAAC,QAAA,GAC5E,GACI,EAACkC,MAAM;UAAA,GAJH,OAAOA,MAAM,EAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhB,CACX,CAAC,EACD,CAAC,CAAC,EAAE,EAAE,CAAC,CAACrB,GAAG,CAAEiC,MAAM,iBAChB/G,OAAA;YAEI4F,OAAO,EAAEA,CAAA,KAAM7E,iBAAiB,CAACqE,QAAQ,CAAC9D,cAAc,CAAC+D,GAAG,CAAC,EAAE,CAAC0B,MAAM,EAAE,KAAK,EAAEzF,cAAc,CAACgF,QAAQ,EAAEhF,cAAc,CAACwE,MAAM,CAAE;YAC/HlB,SAAS,EAAC,2DAA2D;YAAAC,QAAA,GACxE,GACI,EAACkC,MAAM;UAAA,GAJH,QAAQA,MAAM,EAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKjB,CACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAEL,CAAC1E,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,MAAM,kBACjEzB,OAAA,CAAAE,SAAA;UAAA2E,QAAA,gBACI7E,OAAA;YACI4F,OAAO,EAAEA,CAAA,KAAM;cACXtF,OAAO,CAAC8E,QAAQ,CAAC9D,cAAc,CAAC+D,GAAG,CAAC,CAAC;cACrC1F,uBAAuB,CAACgC,MAAM,EAAEyD,QAAQ,CAAC9D,cAAc,CAAC+D,GAAG,CAAC,CAAC;cAC7DzF,KAAK,CAACoH,OAAO,CAAC,4BAA4B1F,cAAc,CAACgF,QAAQ,EAAE,CAAC;YACxE,CAAE;YACF1B,SAAS,EAAE,UAAUvE,IAAI,KAAK+E,QAAQ,CAAC9D,cAAc,CAAC+D,GAAG,CAAC,GAAG,6BAA6B,GAAG,+BAA+B,iCAAkC;YAAAR,QAAA,EAE7JxE,IAAI,KAAK+E,QAAQ,CAAC9D,cAAc,CAAC+D,GAAG,CAAC,GAAG,UAAU,GAAG;UAAmB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,EAGL1E,KAAK,KAAK,GAAG,iBACTzB,OAAA;YAAK4E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACjC7E,OAAA;cACI4F,OAAO,EAAEA,CAAA,KAAM;gBACX3B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE/C,mBAAmB,CAAC;gBAEvD,MAAM8F,aAAa,GAAG,CAAC,CAAC,GAAG9F,mBAAmB,IAAI,EAAE;gBACpD8C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+C,aAAa,CAAC;gBAE3C1H,WAAW,CAACoC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAEtB,IAAI,CAACmF,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAEyB,aAAa,CAAC;gBAC5GrH,KAAK,CAACoH,OAAO,CAAC,WAAWC,aAAa,iBAAiB3F,cAAc,CAACgF,QAAQ,EAAE,CAAC;cACrF,CAAE;cACF1B,SAAS,EAAC,8EAA8E;cAAAC,QAAA,EAAC;YAE7F;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,EAGJ1E,KAAK,KAAK,GAAG,iBACVzB,OAAA;YAAK4E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC7E,OAAA;cACI4F,OAAO,EAAEA,CAAA,KAAM;gBACXrG,WAAW,CAACoC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAEtB,IAAI,CAACmF,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE3E,KAAK,CAAC;cAChF,CAAE;cACF+D,SAAS,EAAC,8EAA8E;cAAAC,QAAA,EAAC;YAE7F;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA;cACI4F,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACjB,MAAMlG,QAAQ,CAACiC,MAAM,CAAC;gBACtB/B,KAAK,CAACoH,OAAO,CAAC,kBAAkB,CAAC;cACrC,CAAE;cACFpC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EACvF;YAED;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA;cACI4F,OAAO,EAAEA,CAAA,KAAM;gBACXrG,WAAW,CAACoC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAEtB,IAAI,CAACmF,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE3E,KAAK,CAAC;cAChF,CAAE;cACF+D,SAAS,EAAC,8EAA8E;cAAAC,QAAA,EAC3F;YAED;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA;cACI4F,OAAO,EAAE,MAAAA,CAAA,KAAY;gBACjBrG,WAAW,CAACoC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAEtB,IAAI,CAACmF,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE3E,KAAK,CAAC;gBAC7E,MAAMnB,QAAQ,CAACiC,MAAM,CAAC;gBACtB/B,KAAK,CAACoH,OAAO,CAAC,kBAAkB,CAAC;cACrC,CAAE;cACFpC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EACvF;YAED;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA;cACI4F,OAAO,EAAEA,CAAA,KAAM;gBACXrG,WAAW,CAACoC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAEd,KAAK,EAAE,MAAM,EAAES,cAAc,CAAC+D,GAAG,EAAEhF,IAAI,CAACmF,QAAQ,CAAC,CAAC,CAAC;cACjH,CAAE;cACFZ,SAAS,EAAC,8EAA8E;cAAAC,QAAA,EAC3F;YAED;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA;cACI4F,OAAO,EAAEA,CAAA,KAAM;gBACXrG,WAAW,CAACoC,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAEd,KAAK,EAAE,OAAO,EAAES,cAAc,CAAC+D,GAAG,EAAEhF,IAAI,CAACmF,QAAQ,CAAC,CAAC,CAAC;cAClH,CAAE;cACFZ,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EACvF;YAED;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR;QAAA,eACH,CACL;MAAA,eACH,CAAC,GAGJ1E,KAAK,KAAK,GAAG,gBACZzB,OAAA;QAAG4E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAA+B;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GACtE,IAEN,eAGDnG,OAAA;QACI4F,OAAO,EAAEA,CAAA,KAAM;UACXhG,KAAK,CAACoH,OAAO,CAAC,mBAAmB,CAAC;UAClCzH,WAAW,CAACoC,MAAM,EAAEX,YAAY,EAAE,QAAQ,EAAES,KAAK,CAAC;UAClD,MAAMyF,YAAY,GAAG,CAAC,GAAGlG,YAAY,CAAC,CAAC8D,GAAG,CAAEqC,CAAC,KAAM;YAC/C,GAAGA,CAAC;YACJC,SAAS,EAAE,KAAK;YAChBC,UAAU,EAAE;UAChB,CAAC,CAAC,CAAC;UACHpG,eAAe,CAACiG,YAAY,CAAC;QACjC,CAAE;QACFtC,SAAS,EAAC,sIAAsI;QAAAC,QAAA,EACnJ;MAED;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETnG,OAAA;QACI4F,OAAO,EAAEA,CAAA,KAAM;UACXrG,WAAW,CAACoC,MAAM,EAAEX,YAAY,EAAET,IAAI,EAAEkB,KAAK,CAAC;UAC9C7B,KAAK,CAACoH,OAAO,CAAC,iCAAiCvF,KAAK,EAAE,CAAC;UACvD;UACA;UACA;UACA;UACA;UACA;QACJ,CAAE;QACFmD,SAAS,EAAC,sIAAsI;QAAAC,QAAA,EACnJ;MAED;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC/F,EAAA,CAjWQD,UAAU;EAAA,QAG0Eb,SAAS,EACsED,OAAO,EACxJI,eAAe,EAIMI,mBAAmB;AAAA;AAAAyH,EAAA,GAT1DnH,UAAU;AAmWnB,eAAeA,UAAU;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}