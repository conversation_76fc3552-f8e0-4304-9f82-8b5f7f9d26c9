{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\PlayerScore.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { usePlayer } from '../context/playerContext';\nimport { useSearchParams } from 'react-router-dom';\nimport { useFirebaseListener } from '../shared/hooks';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PlayerScore({\n  playerColors = {}\n}) {\n  _s();\n  const {\n    scoreList,\n    setScoreList,\n    setPlayerFlashes\n  } = usePlayer();\n  const prevOrder = useRef({});\n  const [params] = useSearchParams();\n  const round = params.get(\"round\") || \"1\";\n  const roomId = params.get(\"roomId\") || \"1\";\n  const {\n    listenToScores\n  } = useFirebaseListener(roomId);\n  useEffect(() => {\n    const unsubscribeScores = listenToScores(roomId, scoreList => {\n      if (Array.isArray(scoreList)) {\n        console.log(\"Received scoreList:\", scoreList);\n        const scoreListWithFlashes = scoreList.map(score => {\n          const flashColor = score.isModified ? score.isCorrect ? \"flash-correct\" : \"flash-incorrect\" : null;\n          console.log(`Player ${score.playerName}: isModified=${score.isModified}, isCorrect=${score.isCorrect}, flashColor=${flashColor}`);\n          return {\n            ...score,\n            flashColor: flashColor,\n            isModified: score.isModified\n          };\n        });\n        setPlayerFlashes(scoreListWithFlashes);\n        const sortedList = [...scoreList].sort((a, b) => parseInt(b.score) - parseInt(a.score));\n        console.log(\"Sorted scoreList:\", sortedList);\n        setScoreList(sortedList);\n      }\n      console.log(\"Updated scoreList:\", scoreList);\n    });\n    return () => {\n      unsubscribeScores();\n    };\n  }, [roomId, setPlayerFlashes, setScoreList, round]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-white font-bold text-lg mb-4 text-center border-b border-blue-400/30 pb-2\",\n      children: \"B\\u1EA3ng \\u0110i\\u1EC3m\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative overflow-hidden\",\n      style: {\n        height: `${Math.max(scoreList.length * 70, 120)}px`\n      },\n      children: scoreList.map((player, index) => {\n        const y = index * 70;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `absolute w-[95%] left-[2.5%] flex items-center bg-slate-700/60 backdrop-blur-sm rounded-lg border border-blue-400/20 shadow-lg mb-2 px-4 py-3\n                                ${player.flashColor ? player.flashColor : \"\"}\n                            `,\n          style: {\n            transform: `translateY(${y}px)`,\n            transition: \"transform 1.8s cubic-bezier(.22,1,.36,1), box-shadow 0.3s\",\n            boxShadow: player.flashColor ? player.flashColor === \"flash-correct\" ? \"0 0 16px 2px #4ade80\" : \"0 0 16px 2px #f87171\" : \"0 4px 12px rgba(0,0,0,0.3)\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full text-white font-bold text-sm mr-3\",\n            children: index + 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mr-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: player.avatar,\n              alt: player.playerName,\n              className: \"w-10 h-10 rounded-full border-2 border-blue-400/50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 33\n            }, this), round === \"4\" && player.stt && playerColors[player.stt] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-lg\",\n              style: {\n                backgroundColor: playerColors[player.stt]\n              },\n              title: `Màu của ${player.playerName}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white font-semibold text-sm mb-1\",\n              children: player.playerName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-600/50 to-cyan-500/50 backdrop-blur-sm text-white text-center py-1 px-3 rounded-lg font-mono text-sm border border-blue-400/30\",\n              children: `${player.score} điểm`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 29\n          }, this)]\n        }, player.playerName, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 25\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 9\n  }, this);\n}\n_s(PlayerScore, \"qA2CQrFOvrS3XDpSeNW8eU9MEn4=\", false, function () {\n  return [usePlayer, useSearchParams, useFirebaseListener];\n});\n_c = PlayerScore;\nexport default PlayerScore;\nvar _c;\n$RefreshReg$(_c, \"PlayerScore\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "usePlayer", "useSearchParams", "useFirebaseListener", "jsxDEV", "_jsxDEV", "PlayerScore", "playerColors", "_s", "scoreList", "setScoreList", "setPlayerFlashes", "prevOrder", "params", "round", "get", "roomId", "listenToScores", "unsubscribeScores", "Array", "isArray", "console", "log", "scoreListWithFlashes", "map", "score", "flashColor", "isModified", "isCorrect", "<PERSON><PERSON><PERSON>", "sortedList", "sort", "a", "b", "parseInt", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "height", "Math", "max", "length", "player", "index", "y", "transform", "transition", "boxShadow", "src", "avatar", "alt", "stt", "backgroundColor", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/PlayerScore.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport { Score } from '../type';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { listenToScores } from '../services/firebaseServices';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport { useFirebaseListener } from '../shared/hooks';\r\n\r\ninterface PlayerScoreProps {\r\n    playerColors?: Record<string, string>;\r\n}\r\n\r\nfunction PlayerScore({ playerColors = {} }: PlayerScoreProps) {\r\n    const {scoreList, setScoreList, setPlayerFlashes } = usePlayer();\r\n    const prevOrder = useRef<{ [name: string]: number }>({});\r\n    const [params] = useSearchParams();\r\n    const round = params.get(\"round\") || \"1\";\r\n    const roomId = params.get(\"roomId\") || \"1\";\r\n\r\n    const { listenToScores } = useFirebaseListener(roomId);\r\n\r\n    useEffect(() => {\r\n        const unsubscribeScores = listenToScores(roomId, (scoreList) => {\r\n            if (Array.isArray(scoreList)) {\r\n                console.log(\"Received scoreList:\", scoreList);\r\n                const scoreListWithFlashes = scoreList.map((score: Score) => {\r\n                    const flashColor = score.isModified ? score.isCorrect ? \"flash-correct\" : \"flash-incorrect\" : null;\r\n                    console.log(`Player ${score.playerName}: isModified=${score.isModified}, isCorrect=${score.isCorrect}, flashColor=${flashColor}`);\r\n                    return {\r\n                        ...score,\r\n                        flashColor: flashColor,\r\n                        isModified: score.isModified\r\n                    };\r\n                });\r\n                setPlayerFlashes(scoreListWithFlashes);\r\n                const sortedList = [...scoreList].sort((a, b) => parseInt(b.score) - parseInt(a.score));\r\n                console.log(\"Sorted scoreList:\", sortedList);\r\n                setScoreList(sortedList);\r\n            }\r\n            console.log(\"Updated scoreList:\", scoreList);\r\n        });\r\n        return () => {\r\n            unsubscribeScores();\r\n        };\r\n    }, [roomId, setPlayerFlashes, setScoreList, round]);\r\n\r\n    return (\r\n        <div className=\"bg-slate-800/70 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-xl p-4\">\r\n            <div className=\"text-white font-bold text-lg mb-4 text-center border-b border-blue-400/30 pb-2\">\r\n                Bảng Điểm\r\n            </div>\r\n            <div \r\n                className=\"relative overflow-hidden\"\r\n                style={{ height: `${Math.max(scoreList.length * 70, 120)}px` }}\r\n            >\r\n                {scoreList.map((player: Score, index: number) => {\r\n                    const y = index * 70;\r\n                    return (\r\n                        <div\r\n                            key={player.playerName}\r\n                            className={`absolute w-[95%] left-[2.5%] flex items-center bg-slate-700/60 backdrop-blur-sm rounded-lg border border-blue-400/20 shadow-lg mb-2 px-4 py-3\r\n                                ${player.flashColor ? player.flashColor : \"\"}\r\n                            `}\r\n                            style={{\r\n                                transform: `translateY(${y}px)`,\r\n                                transition: \"transform 1.8s cubic-bezier(.22,1,.36,1), box-shadow 0.3s\",\r\n                                boxShadow: player.flashColor\r\n                                    ? player.flashColor === \"flash-correct\"\r\n                                        ? \"0 0 16px 2px #4ade80\"\r\n                                        : \"0 0 16px 2px #f87171\"\r\n                                    : \"0 4px 12px rgba(0,0,0,0.3)\",\r\n                            }}\r\n                        >\r\n                            <div className=\"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-full text-white font-bold text-sm mr-3\">\r\n                                {index + 1}\r\n                            </div>\r\n                            <div className=\"relative mr-3\">\r\n                                <img\r\n                                    src={player.avatar}\r\n                                    alt={player.playerName}\r\n                                    className=\"w-10 h-10 rounded-full border-2 border-blue-400/50\"\r\n                                />\r\n                                {/* Color indicator for Round 4 */}\r\n                                {round === \"4\" && player.stt && playerColors[player.stt] && (\r\n                                    <div\r\n                                        className=\"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white shadow-lg\"\r\n                                        style={{ backgroundColor: playerColors[player.stt] }}\r\n                                        title={`Màu của ${player.playerName}`}\r\n                                    ></div>\r\n                                )}\r\n                            </div>\r\n                            <div className=\"flex-1\">\r\n                                <p className=\"text-white font-semibold text-sm mb-1\">{player.playerName}</p>\r\n                                <div className=\"bg-gradient-to-r from-blue-600/50 to-cyan-500/50 backdrop-blur-sm text-white text-center py-1 px-3 rounded-lg font-mono text-sm border border-blue-400/30\">\r\n                                    {`${player.score} điểm`}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    );\r\n                })}\r\n            </div>\r\n\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default PlayerScore;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEhD,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SAASC,mBAAmB,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMtD,SAASC,WAAWA,CAAC;EAAEC,YAAY,GAAG,CAAC;AAAoB,CAAC,EAAE;EAAAC,EAAA;EAC1D,MAAM;IAACC,SAAS;IAAEC,YAAY;IAAEC;EAAiB,CAAC,GAAGV,SAAS,CAAC,CAAC;EAChE,MAAMW,SAAS,GAAGZ,MAAM,CAA6B,CAAC,CAAC,CAAC;EACxD,MAAM,CAACa,MAAM,CAAC,GAAGX,eAAe,CAAC,CAAC;EAClC,MAAMY,KAAK,GAAGD,MAAM,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EACxC,MAAMC,MAAM,GAAGH,MAAM,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG;EAE1C,MAAM;IAAEE;EAAe,CAAC,GAAGd,mBAAmB,CAACa,MAAM,CAAC;EAEtDjB,SAAS,CAAC,MAAM;IACZ,MAAMmB,iBAAiB,GAAGD,cAAc,CAACD,MAAM,EAAGP,SAAS,IAAK;MAC5D,IAAIU,KAAK,CAACC,OAAO,CAACX,SAAS,CAAC,EAAE;QAC1BY,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,SAAS,CAAC;QAC7C,MAAMc,oBAAoB,GAAGd,SAAS,CAACe,GAAG,CAAEC,KAAY,IAAK;UACzD,MAAMC,UAAU,GAAGD,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACG,SAAS,GAAG,eAAe,GAAG,iBAAiB,GAAG,IAAI;UAClGP,OAAO,CAACC,GAAG,CAAC,UAAUG,KAAK,CAACI,UAAU,gBAAgBJ,KAAK,CAACE,UAAU,eAAeF,KAAK,CAACG,SAAS,gBAAgBF,UAAU,EAAE,CAAC;UACjI,OAAO;YACH,GAAGD,KAAK;YACRC,UAAU,EAAEA,UAAU;YACtBC,UAAU,EAAEF,KAAK,CAACE;UACtB,CAAC;QACL,CAAC,CAAC;QACFhB,gBAAgB,CAACY,oBAAoB,CAAC;QACtC,MAAMO,UAAU,GAAG,CAAC,GAAGrB,SAAS,CAAC,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKC,QAAQ,CAACD,CAAC,CAACR,KAAK,CAAC,GAAGS,QAAQ,CAACF,CAAC,CAACP,KAAK,CAAC,CAAC;QACvFJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEQ,UAAU,CAAC;QAC5CpB,YAAY,CAACoB,UAAU,CAAC;MAC5B;MACAT,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEb,SAAS,CAAC;IAChD,CAAC,CAAC;IACF,OAAO,MAAM;MACTS,iBAAiB,CAAC,CAAC;IACvB,CAAC;EACL,CAAC,EAAE,CAACF,MAAM,EAAEL,gBAAgB,EAAED,YAAY,EAAEI,KAAK,CAAC,CAAC;EAEnD,oBACIT,OAAA;IAAK8B,SAAS,EAAC,qFAAqF;IAAAC,QAAA,gBAChG/B,OAAA;MAAK8B,SAAS,EAAC,gFAAgF;MAAAC,QAAA,EAAC;IAEhG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNnC,OAAA;MACI8B,SAAS,EAAC,0BAA0B;MACpCM,KAAK,EAAE;QAAEC,MAAM,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACnC,SAAS,CAACoC,MAAM,GAAG,EAAE,EAAE,GAAG,CAAC;MAAK,CAAE;MAAAT,QAAA,EAE9D3B,SAAS,CAACe,GAAG,CAAC,CAACsB,MAAa,EAAEC,KAAa,KAAK;QAC7C,MAAMC,CAAC,GAAGD,KAAK,GAAG,EAAE;QACpB,oBACI1C,OAAA;UAEI8B,SAAS,EAAE;AACvC,kCAAkCW,MAAM,CAACpB,UAAU,GAAGoB,MAAM,CAACpB,UAAU,GAAG,EAAE;AAC5E,6BAA8B;UACFe,KAAK,EAAE;YACHQ,SAAS,EAAE,cAAcD,CAAC,KAAK;YAC/BE,UAAU,EAAE,2DAA2D;YACvEC,SAAS,EAAEL,MAAM,CAACpB,UAAU,GACtBoB,MAAM,CAACpB,UAAU,KAAK,eAAe,GACjC,sBAAsB,GACtB,sBAAsB,GAC1B;UACV,CAAE;UAAAU,QAAA,gBAEF/B,OAAA;YAAK8B,SAAS,EAAC,oIAAoI;YAAAC,QAAA,EAC9IW,KAAK,GAAG;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B/B,OAAA;cACI+C,GAAG,EAAEN,MAAM,CAACO,MAAO;cACnBC,GAAG,EAAER,MAAM,CAACjB,UAAW;cACvBM,SAAS,EAAC;YAAoD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,EAED1B,KAAK,KAAK,GAAG,IAAIgC,MAAM,CAACS,GAAG,IAAIhD,YAAY,CAACuC,MAAM,CAACS,GAAG,CAAC,iBACpDlD,OAAA;cACI8B,SAAS,EAAC,kFAAkF;cAC5FM,KAAK,EAAE;gBAAEe,eAAe,EAAEjD,YAAY,CAACuC,MAAM,CAACS,GAAG;cAAE,CAAE;cACrDE,KAAK,EAAE,WAAWX,MAAM,CAACjB,UAAU;YAAG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACNnC,OAAA;YAAK8B,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACnB/B,OAAA;cAAG8B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAEU,MAAM,CAACjB;YAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EnC,OAAA;cAAK8B,SAAS,EAAC,2JAA2J;cAAAC,QAAA,EACrK,GAAGU,MAAM,CAACrB,KAAK;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GArCDM,MAAM,CAACjB,UAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsCrB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEL,CAAC;AAEd;AAAChC,EAAA,CA5FQF,WAAW;EAAA,QACqCL,SAAS,EAE7CC,eAAe,EAILC,mBAAmB;AAAA;AAAAuD,EAAA,GAPzCpD,WAAW;AA8FpB,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}