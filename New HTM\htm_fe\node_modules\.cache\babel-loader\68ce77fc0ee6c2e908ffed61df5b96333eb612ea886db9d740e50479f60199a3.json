{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\HostManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useHost } from '../context/hostContext';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { usePlayer } from '../context/playerContext';\nimport { playSound } from './services';\nimport http from '../services/http';\nimport { CheckCircleIcon, ArrowRightCircleIcon, EyeIcon, ClockIcon, PlayCircleIcon, SpeakerWaveIcon, MusicalNoteIcon, DocumentTextIcon, EyeSlashIcon, QuestionMarkCircleIcon } from \"@heroicons/react/24/solid\";\nimport { toast } from 'react-toastify';\nimport HostQuestionPreview from './HostQuestionPreview';\nimport HostGuideModal from './HostGuideModal';\nimport PlayerColorSelector from './PlayerColorSelector';\nimport useTokenRefresh from '../hooks/useTokenRefresh';\nimport useGameApi from '../shared/hooks/api/useGameApi';\nimport { getQuestions } from '../app/store/slices/gameSlice';\nimport { useAppDispatch, useAppSelector } from '../app/store';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HostManagement = () => {\n  _s();\n  const {\n    handleNextQuestion,\n    handleShowAnswer,\n    handleStartTime,\n    handleStartRound,\n    handleCorrectAnswer,\n    currentAnswer,\n    playerScores,\n    setPlayerScores,\n    currentQuestionIndex,\n    setCurrentQuestionIndex,\n    hostInitialGrid,\n    playerColors,\n    setPlayerColors,\n    inGameQuestionIndex,\n    setInGameQuestionIndex\n  } = useHost();\n  const {\n    initialGrid,\n    selectedTopic,\n    easyQuestionNumber,\n    mediumQuestionNumber,\n    hardQuestionNumber,\n    setEasyQuestionNumber,\n    setMediumQuestionNumber,\n    setHardQuestionNumber,\n    level,\n    setAnswerList\n  } = usePlayer();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const testName = searchParams.get(\"testName\") || \"1\";\n  const roomId = searchParams.get(\"roomId\") || \"1\";\n  const [showingRules, setShowingRules] = useState(false);\n  const [showGuideModal, setShowGuideModal] = useState(false);\n  const [showColorSelector, setShowColorSelector] = useState(false);\n  const dispatch = useAppDispatch();\n  const {\n    startRound,\n    broadcastAnswers,\n    sendCorrectAnswer\n  } = useGameApi();\n  const {\n    currentRound\n  } = useAppSelector(state => state.game);\n  // Initialize token refresh for host\n  useTokenRefresh();\n  // const handleRoundChange = async (delta: number) => {\n  //     console.log(\"currentRound\", currentRound)\n  //     const newRound = parseInt(currentRound) + delta;\n  //     console.log(\"new round\", newRound)\n  //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\n  //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\n  //     }\n\n  //     // Clear frontend state\n  //     setAnswerList([]);\n\n  //     // Clear Firebase data\n  //     await deletePath(roomId, \"questions\");\n  //     await deletePath(roomId, \"answers\");\n  //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\n  //     await deletePath(roomId, \"turn\"); // Clear turn assignments\n  //     await deletePath(roomId, \"isModified\"); // Clear isModified state\n  //     // Don't clear showRules here - let host control modal display manually\n  //     setShowingRules(false); // Reset rules button state\n  // };\n\n  const handleStartRoundClick = async () => {\n    try {\n      await startRound(roomId);\n      toast.success(`Đã bắt đầu vòng thi ${currentRound}`);\n    } catch (error) {\n      console.error('Error starting round:', error);\n      toast.error('Lỗi khi bắt đầu vòng thi');\n    }\n  };\n  const handleBroadcastAnswersClick = async () => {\n    try {\n      await broadcastAnswers(roomId);\n      toast.success('Đã gửi câu trả lời của tất cả các thí sinh đến người chơi!');\n    } catch (error) {\n      console.error('Error broadcasting answers:', error);\n      toast.error('Lỗi khi gửi câu trả lời của tất cả các thí sinh đến người chơi');\n    }\n  };\n  const handleNextQuestionclick = async () => {\n    try {\n      dispatch(nex);\n      await dispatch(getQuestions());\n      toast.success('Đã gửi câu trả lời của tất cả các thí sinh đến người chơi!');\n    } catch (error) {\n      console.error('Error broadcasting answers:', error);\n      toast.error('Lỗi khi gửi câu trả lời của tất cả các thí sinh đến người chơi');\n    }\n  };\n  const handleSendCorrectAnswer = async () => {\n    try {\n      await sendCorrectAnswer(roomId);\n      toast.success('Đã hiển thị câu trả lời đúng cho người chơi!');\n    } catch (error) {\n      console.error('Error sending correct answer:', error);\n      toast.error('Lỗi khi hiển thị câu trả lời đúng cho người chơi!');\n    }\n  };\n  const handleToggleRules = async () => {\n    try {\n      if (showingRules) {\n        // Hide rules\n        await http.post('game/rules/hide', true, {}, {\n          room_id: roomId\n        });\n        setShowingRules(false);\n        toast.success('Đã ẩn luật thi');\n      } else {\n        // Show rules\n        await http.post('room/rules/show', true, {}, {\n          room_id: roomId,\n          round_number: currentRound\n        });\n        setShowingRules(true);\n        toast.success(`Đã hiển thị luật thi vòng ${currentRound}`);\n      }\n    } catch (error) {\n      console.error('Error toggling rules:', error);\n      toast.error('Lỗi khi thay đổi hiển thị luật thi');\n    }\n  };\n  const handleSavePlayerColors = colors => {\n    setPlayerColors(colors);\n    toast.success('Đã lưu màu cho thí sinh!');\n  };\n  useEffect(() => {\n    setInGameQuestionIndex(1);\n    // Clear rules when entering new round to prevent auto-show\n    setShowingRules(false);\n    // Also clear rules from Firebase to ensure clean state\n    http.post('game/rules/hide', true, {}, {\n      room_id: roomId\n    }).catch(console.error);\n  }, [currentRound]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-4 lg:p-6 mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(HostQuestionPreview, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowGuideModal(true),\n          className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 font-medium text-sm\",\n          title: \"H\\u01B0\\u1EDBng d\\u1EABn host\",\n          children: [/*#__PURE__*/_jsxDEV(QuestionMarkCircleIcon, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this), \"H\\u01B0\\u1EDBng d\\u1EABn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-400 text-sm\",\n        children: [\"V\\xF2ng \", currentRound, \" - \", currentRound === \"1\" ? \"NHỔ NEO\" : currentRound === \"2\" ? \"VƯỢT SÓNG\" : currentRound === \"3\" ? \"BỨT PHÁ\" : currentRound === \"4\" ? \"CHINH PHỤC\" : \"PHÂN LƯỢT\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [currentRound !== \"4\" && /*#__PURE__*/_jsxDEV(\"input\", {\n          min: 0,\n          value: inGameQuestionIndex,\n          onChange: e => {\n            const val = e.target.value;\n            if (val === \"\") {\n              setInGameQuestionIndex(0);\n            } else {\n              setInGameQuestionIndex(Number(val));\n            }\n          },\n          className: \"w-16 px-2 py-2 rounded-lg border border-blue-400 bg-slate-700 text-white text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-400\",\n          style: {\n            minWidth: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 25\n        }, this), currentRound !== \"4\" && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: async () => {\n            try {\n              setCurrentQuestionIndex(inGameQuestionIndex.toString());\n\n              // Fetch and display the specified question\n              if (currentRound === \"3\") {\n                await handleNextQuestion(selectedTopic, undefined, inGameQuestionIndex.toString());\n              } else if (currentRound === \"4\") {\n                await handleNextQuestion(undefined, level, inGameQuestionIndex.toString());\n              } else {\n                await handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\n              }\n              toast.success(`Đã chuyển đến câu hỏi số: ${inGameQuestionIndex}`);\n            } catch (error) {\n              console.error(\"Error jumping to question:\", error);\n              toast.error(\"Lỗi khi chuyển đến câu hỏi!\");\n            }\n          },\n          className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowRightCircleIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 29\n          }, this), \"CHUY\\u1EC2N \\u0110\\u1EBEN C\\xC2U H\\u1ECEI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleStartRoundClick,\n          className: \"w-full flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base\",\n          children: [/*#__PURE__*/_jsxDEV(PlayCircleIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 21\n          }, this), \"B\\u1EAET \\u0110\\u1EA6U V\\xD2NG THI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleNextQuestionclick,\n        className: \"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ArrowRightCircleIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 25\n        }, this), \"C\\xC2U H\\u1ECEI TI\\u1EBEP THEO\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          handleStartTime();\n          toast.success(\"Đã bắt đầu đếm giờ!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 21\n        }, this), \"B\\u1EAET \\u0110\\u1EA6U \\u0110\\u1EBEM GI\\u1EDC\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleBroadcastAnswersClick,\n        className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(EyeIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 21\n        }, this), \"HI\\u1EC6N C\\xC2U TR\\u1EA2 L\\u1EDCI TH\\xCD SINH\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSendCorrectAnswer,\n        className: \"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 21\n        }, this), \"HI\\u1EC6N \\u0110\\xC1P \\xC1N \\u0110\\xDANG\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          playSound(roomId, currentRound);\n          toast.success(`Đã chạy âm thanh cho vòng thi ${currentRound}`);\n        },\n        className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(SpeakerWaveIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 21\n        }, this), \"CH\\u1EA0Y \\xC2M THANH B\\u1EAET \\u0110\\u1EA6U V\\xD2NG THI\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          playSound(roomId, \"opening\");\n          toast.success(\"Đã chạy âm thanh mở đầu!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(MusicalNoteIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 21\n        }, this), \"CH\\u1EA0Y \\xC2M THANH M\\u1EDE \\u0110\\u1EA6U\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleToggleRules,\n        className: `flex items-center ${showingRules ? 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-red-400/50' : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-green-400/50'} text-white p-2 lg:p-3 rounded-lg shadow-md border transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full`,\n        children: [showingRules ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 25\n        }, this), showingRules ? 'ẨN LUẬT THI' : 'HIỂN THỊ LUẬT THI']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(HostGuideModal, {\n      isOpen: showGuideModal,\n      onClose: () => setShowGuideModal(false),\n      round: currentRound\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(PlayerColorSelector, {\n      isOpen: showColorSelector,\n      onClose: () => setShowColorSelector(false),\n      players: playerScores,\n      onSaveColors: handleSavePlayerColors,\n      currentColors: playerColors\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 9\n  }, this);\n};\n_s(HostManagement, \"03y3xdgSL3rFhdXBe60UCUaj0RY=\", false, function () {\n  return [useHost, usePlayer, useSearchParams, useNavigate, useAppDispatch, useGameApi, useAppSelector, useTokenRefresh];\n});\n_c = HostManagement;\nexport default HostManagement;\nvar _c;\n$RefreshReg$(_c, \"HostManagement\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useHost", "useSearchParams", "useNavigate", "usePlayer", "playSound", "http", "CheckCircleIcon", "ArrowRightCircleIcon", "EyeIcon", "ClockIcon", "PlayCircleIcon", "SpeakerWaveIcon", "MusicalNoteIcon", "DocumentTextIcon", "EyeSlashIcon", "QuestionMarkCircleIcon", "toast", "HostQuestionPreview", "HostGuideModal", "PlayerColorSelector", "useTokenRefresh", "useGameApi", "getQuestions", "useAppDispatch", "useAppSelector", "jsxDEV", "_jsxDEV", "HostManagement", "_s", "handleNextQuestion", "handleShowAnswer", "handleStartTime", "handleStartRound", "handleCorrectAnswer", "currentAnswer", "playerScores", "setPlayerScores", "currentQuestionIndex", "setCurrentQuestionIndex", "hostInitialGrid", "playerColors", "setPlayerColors", "inGameQuestionIndex", "setInGameQuestionIndex", "initialGrid", "selectedTopic", "easyQuestionNumber", "mediumQuestionNumber", "hardQuestionNumber", "setEasyQuestionNumber", "setMediumQuestionNumber", "setHardQuestionNumber", "level", "setAnswerList", "searchParams", "navigate", "testName", "get", "roomId", "showingRules", "setShowingRules", "showGuideModal", "setShowGuideModal", "showColorSelector", "setShowColorSelector", "dispatch", "startRound", "broadcastAnswers", "sendCorrectAnswer", "currentRound", "state", "game", "handleStartRoundClick", "success", "error", "console", "handleBroadcastAnswersClick", "handleNextQuestionclick", "nex", "handleSendCorrectAnswer", "handleToggleRules", "post", "room_id", "round_number", "handleSavePlayerColors", "colors", "catch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "min", "value", "onChange", "e", "val", "target", "Number", "style", "min<PERSON><PERSON><PERSON>", "toString", "undefined", "isOpen", "onClose", "round", "players", "onSaveColors", "currentColors", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/HostManagement.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\r\nimport { useHost } from '../context/hostContext';\r\nimport { useSearchParams, useNavigate } from 'react-router-dom';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { openBuzz } from './services';\r\nimport { playSound } from './services';\r\nimport { deletePath } from '../services/firebaseServices';\r\nimport { updateScore } from '../pages/Host/Management/service';\r\nimport http from '../services/http';\r\nimport {\r\n    CheckCircleIcon,\r\n    BellAlertIcon,\r\n    ArrowRightCircleIcon,\r\n    EyeIcon,\r\n    ClockIcon,\r\n    PlayCircleIcon,\r\n    SpeakerWaveIcon,\r\n    MusicalNoteIcon,\r\n    DocumentTextIcon,\r\n    EyeSlashIcon,\r\n    QuestionMarkCircleIcon,\r\n    PaintBrushIcon,\r\n} from \"@heroicons/react/24/solid\";\r\nimport { toast } from 'react-toastify';\r\nimport HostQuestionPreview from './HostQuestionPreview';\r\nimport HostGuideModal from './HostGuideModal';\r\nimport PlayerColorSelector from './PlayerColorSelector';\r\nimport useTokenRefresh from '../hooks/useTokenRefresh';\r\nimport useGameApi from '../shared/hooks/api/useGameApi';\r\nimport { getQuestions } from '../app/store/slices/gameSlice';\r\nimport { useAppDispatch, useAppSelector } from '../app/store';\r\n\r\n\r\nconst HostManagement = () => {\r\n    const {\r\n        handleNextQuestion,\r\n        handleShowAnswer,\r\n        handleStartTime,\r\n        handleStartRound,\r\n        handleCorrectAnswer,\r\n        currentAnswer,\r\n        playerScores,\r\n        setPlayerScores,\r\n        currentQuestionIndex,\r\n        setCurrentQuestionIndex,\r\n        hostInitialGrid,\r\n        playerColors,\r\n        setPlayerColors,\r\n        inGameQuestionIndex,\r\n        setInGameQuestionIndex\r\n    } = useHost();\r\n\r\n    const { initialGrid, selectedTopic, easyQuestionNumber, mediumQuestionNumber, hardQuestionNumber, setEasyQuestionNumber, setMediumQuestionNumber, setHardQuestionNumber, level, setAnswerList } = usePlayer()\r\n\r\n    const [searchParams] = useSearchParams();\r\n    const navigate = useNavigate();\r\n\r\n\r\n    const testName = searchParams.get(\"testName\") || \"1\"\r\n    const roomId = searchParams.get(\"roomId\") || \"1\"\r\n    const [showingRules, setShowingRules] = useState(false);\r\n    const [showGuideModal, setShowGuideModal] = useState(false);\r\n    const [showColorSelector, setShowColorSelector] = useState(false);\r\n\r\n    const dispatch = useAppDispatch();\r\n\r\n    const {startRound, broadcastAnswers, sendCorrectAnswer } = useGameApi()\r\n    const {currentRound} = useAppSelector(state => state.game)\r\n    // Initialize token refresh for host\r\n    useTokenRefresh();\r\n    // const handleRoundChange = async (delta: number) => {\r\n    //     console.log(\"currentRound\", currentRound)\r\n    //     const newRound = parseInt(currentRound) + delta;\r\n    //     console.log(\"new round\", newRound)\r\n    //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\r\n    //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\r\n    //     }\r\n\r\n    //     // Clear frontend state\r\n    //     setAnswerList([]);\r\n\r\n    //     // Clear Firebase data\r\n    //     await deletePath(roomId, \"questions\");\r\n    //     await deletePath(roomId, \"answers\");\r\n    //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\r\n    //     await deletePath(roomId, \"turn\"); // Clear turn assignments\r\n    //     await deletePath(roomId, \"isModified\"); // Clear isModified state\r\n    //     // Don't clear showRules here - let host control modal display manually\r\n    //     setShowingRules(false); // Reset rules button state\r\n    // };\r\n\r\n    const handleStartRoundClick = async () => {\r\n        try {\r\n            await startRound(roomId);\r\n            toast.success(`Đã bắt đầu vòng thi ${currentRound}`);\r\n        } catch (error) {\r\n            console.error('Error starting round:', error);\r\n            toast.error('Lỗi khi bắt đầu vòng thi');\r\n        }\r\n    }\r\n\r\n    const handleBroadcastAnswersClick = async () => {\r\n        try {\r\n            await broadcastAnswers(roomId);\r\n            toast.success('Đã gửi câu trả lời của tất cả các thí sinh đến người chơi!');\r\n        } catch (error) {\r\n            console.error('Error broadcasting answers:', error);\r\n            toast.error('Lỗi khi gửi câu trả lời của tất cả các thí sinh đến người chơi');\r\n        }\r\n    }\r\n\r\n    const handleNextQuestionclick = async () => {\r\n        try {\r\n            dispatch(nex)\r\n            await dispatch(getQuestions());\r\n            toast.success('Đã gửi câu trả lời của tất cả các thí sinh đến người chơi!');\r\n        } catch (error) {\r\n            console.error('Error broadcasting answers:', error);\r\n            toast.error('Lỗi khi gửi câu trả lời của tất cả các thí sinh đến người chơi');\r\n        }\r\n    }\r\n\r\n    const handleSendCorrectAnswer = async () => {\r\n        try {\r\n            await sendCorrectAnswer(roomId);\r\n            toast.success('Đã hiển thị câu trả lời đúng cho người chơi!');\r\n        } catch (error) {\r\n            console.error('Error sending correct answer:', error);\r\n            toast.error('Lỗi khi hiển thị câu trả lời đúng cho người chơi!');\r\n        }\r\n    }\r\n\r\n    const handleToggleRules = async () => {\r\n        try {\r\n            if (showingRules) {\r\n                // Hide rules\r\n                await http.post('game/rules/hide', true, {}, { room_id: roomId });\r\n                setShowingRules(false);\r\n                toast.success('Đã ẩn luật thi');\r\n            } else {\r\n                // Show rules\r\n                await http.post('room/rules/show', true, {}, {\r\n                    room_id: roomId,\r\n                    round_number: currentRound\r\n                });\r\n                setShowingRules(true);\r\n                toast.success(`Đã hiển thị luật thi vòng ${currentRound}`);\r\n            }\r\n        } catch (error) {\r\n            console.error('Error toggling rules:', error);\r\n            toast.error('Lỗi khi thay đổi hiển thị luật thi');\r\n        }\r\n    };\r\n\r\n    const handleSavePlayerColors = (colors: Record<string, string>) => {\r\n        setPlayerColors(colors);\r\n        toast.success('Đã lưu màu cho thí sinh!');\r\n    };\r\n\r\n    useEffect(() => {\r\n        setInGameQuestionIndex(1);\r\n        // Clear rules when entering new round to prevent auto-show\r\n        setShowingRules(false);\r\n        // Also clear rules from Firebase to ensure clean state\r\n        http.post('game/rules/hide', true, {}, { room_id: roomId }).catch(console.error);\r\n    }, [currentRound]);\r\n\r\n    return (\r\n        <div className=\"bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-4 lg:p-6 mt-4\">\r\n\r\n            {/* Host Question Preview */}\r\n            <HostQuestionPreview />\r\n\r\n            {/* Guide and Color Selection */}\r\n            <div className=\"flex items-center justify-between mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <button\r\n                        onClick={() => setShowGuideModal(true)}\r\n                        className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 font-medium text-sm\"\r\n                        title=\"Hướng dẫn host\"\r\n                    >\r\n                        <QuestionMarkCircleIcon className=\"w-5 h-5 mr-2\" />\r\n                        Hướng dẫn\r\n                    </button>\r\n\r\n                </div>\r\n\r\n                <div className=\"text-gray-400 text-sm\">\r\n                    Vòng {currentRound} - {currentRound === \"1\" ? \"NHỔ NEO\" : currentRound === \"2\" ? \"VƯỢT SÓNG\" : currentRound === \"3\" ? \"BỨT PHÁ\" : currentRound === \"4\" ? \"CHINH PHỤC\" : \"PHÂN LƯỢT\"}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Host actions - First row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4 mb-4\">\r\n\r\n                {/* <button\r\n                    onClick={() => openBuzz(roomId)}\r\n                    className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-3 lg:p-4 rounded-xl shadow-lg border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <BellAlertIcon className=\"w-5 h-5 mr-2\" />\r\n\r\n                    MỞ BẤM CHUÔNG\r\n                </button> */}\r\n                <div className=\"flex items-center gap-3\">\r\n                    {/* Current Question Index Input - Disabled for Round 4 */}\r\n                    {currentRound !== \"4\" && (\r\n                        <input\r\n                            min={0}\r\n                            value={inGameQuestionIndex}\r\n                            onChange={e => {\r\n                                const val = e.target.value;\r\n                                if (val === \"\") {\r\n                                    setInGameQuestionIndex(0);\r\n                                } else {\r\n                                    setInGameQuestionIndex(Number(val));\r\n                                }\r\n                            }}\r\n                            className=\"w-16 px-2 py-2 rounded-lg border border-blue-400 bg-slate-700 text-white text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-400\"\r\n                            style={{ minWidth: 0 }}\r\n                        />\r\n                    )}\r\n                    {currentRound !== \"4\" && (\r\n                        <button\r\n                        onClick={async () => {\r\n                            try {\r\n                                setCurrentQuestionIndex(inGameQuestionIndex.toString());\r\n\r\n                                // Fetch and display the specified question\r\n                                if (currentRound === \"3\") {\r\n                                    await handleNextQuestion(selectedTopic, undefined, inGameQuestionIndex.toString());\r\n                                } else if (currentRound === \"4\") {\r\n                                    await handleNextQuestion(undefined, level, inGameQuestionIndex.toString());\r\n                                } else {\r\n                                    await handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\r\n                                }\r\n\r\n                                toast.success(`Đã chuyển đến câu hỏi số: ${inGameQuestionIndex}`);\r\n                            } catch (error) {\r\n                                console.error(\"Error jumping to question:\", error);\r\n                                toast.error(\"Lỗi khi chuyển đến câu hỏi!\");\r\n                            }\r\n                        }}\r\n                        className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                    >\r\n                            <ArrowRightCircleIcon className=\"w-4 h-4 mr-2\" />\r\n                            CHUYỂN ĐẾN CÂU HỎI\r\n                        </button>\r\n                    )}\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-3\">\r\n                    <button\r\n                    onClick={handleStartRoundClick}\r\n                    className=\"w-full flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base\"\r\n                >\r\n                    <PlayCircleIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    BẮT ĐẦU VÒNG THI\r\n                </button>\r\n                    {/* Current Question Index Input */}\r\n                    \r\n                </div>\r\n                <button\r\n                        onClick={handleNextQuestionclick}\r\n                        className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                    >\r\n                        <ArrowRightCircleIcon className=\"w-4 h-4 mr-2\" />\r\n                        CÂU HỎI TIẾP THEO\r\n                    </button>\r\n                    <button\r\n                    onClick={() => {\r\n                        handleStartTime()\r\n                        toast.success(\"Đã bắt đầu đếm giờ!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <ClockIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    BẮT ĐẦU ĐẾM GIỜ\r\n                </button>\r\n                <button\r\n                    onClick={handleBroadcastAnswersClick}\r\n                    className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <EyeIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    HIỆN CÂU TRẢ LỜI THÍ SINH\r\n                </button>\r\n               \r\n            </div>\r\n\r\n            {/* Show Answer and Start Timer - Second row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4 mb-4\">\r\n                 <button\r\n                    onClick={handleSendCorrectAnswer}\r\n                    className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <CheckCircleIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    HIỆN ĐÁP ÁN ĐÚNG\r\n                </button>\r\n            </div>\r\n\r\n            {/* Sound controls - Fourth row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4\">\r\n                <button\r\n                    onClick={() => {\r\n                        playSound(roomId, currentRound)\r\n                        toast.success(`Đã chạy âm thanh cho vòng thi ${currentRound}`);\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <SpeakerWaveIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    CHẠY ÂM THANH BẮT ĐẦU VÒNG THI\r\n                </button>\r\n                <button\r\n                    onClick={() => {\r\n                        playSound(roomId, \"opening\")\r\n                        toast.success(\"Đã chạy âm thanh mở đầu!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <MusicalNoteIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    CHẠY ÂM THANH MỞ ĐẦU\r\n                </button>\r\n                <button\r\n                    onClick={handleToggleRules}\r\n                    className={`flex items-center ${\r\n                        showingRules\r\n                            ? 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-red-400/50'\r\n                            : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-green-400/50'\r\n                    } text-white p-2 lg:p-3 rounded-lg shadow-md border transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full`}\r\n                >\r\n                    {showingRules ? (\r\n                        <EyeSlashIcon className=\"w-4 h-4 mr-2\" />\r\n                    ) : (\r\n                        <DocumentTextIcon className=\"w-4 h-4 mr-2\" />\r\n                    )}\r\n                    {showingRules ? 'ẨN LUẬT THI' : 'HIỂN THỊ LUẬT THI'}\r\n                </button>\r\n            </div>\r\n\r\n            {/* Modals */}\r\n            <HostGuideModal\r\n                isOpen={showGuideModal}\r\n                onClose={() => setShowGuideModal(false)}\r\n                round={currentRound}\r\n            />\r\n\r\n            <PlayerColorSelector\r\n                isOpen={showColorSelector}\r\n                onClose={() => setShowColorSelector(false)}\r\n                players={playerScores}\r\n                onSaveColors={handleSavePlayerColors}\r\n                currentColors={playerColors}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HostManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,SAAS,QAAQ,YAAY;AAGtC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,SACIC,eAAe,EAEfC,oBAAoB,EACpBC,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,YAAY,EACZC,sBAAsB,QAEnB,2BAA2B;AAClC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IACFC,kBAAkB;IAClBC,gBAAgB;IAChBC,eAAe;IACfC,gBAAgB;IAChBC,mBAAmB;IACnBC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,uBAAuB;IACvBC,eAAe;IACfC,YAAY;IACZC,eAAe;IACfC,mBAAmB;IACnBC;EACJ,CAAC,GAAG3C,OAAO,CAAC,CAAC;EAEb,MAAM;IAAE4C,WAAW;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC,qBAAqB;IAAEC,uBAAuB;IAAEC,qBAAqB;IAAEC,KAAK;IAAEC;EAAc,CAAC,GAAGlD,SAAS,CAAC,CAAC;EAE7M,MAAM,CAACmD,YAAY,CAAC,GAAGrD,eAAe,CAAC,CAAC;EACxC,MAAMsD,QAAQ,GAAGrD,WAAW,CAAC,CAAC;EAG9B,MAAMsD,QAAQ,GAAGF,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG;EACpD,MAAMC,MAAM,GAAGJ,YAAY,CAACG,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG;EAChD,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMkE,QAAQ,GAAG1C,cAAc,CAAC,CAAC;EAEjC,MAAM;IAAC2C,UAAU;IAAEC,gBAAgB;IAAEC;EAAkB,CAAC,GAAG/C,UAAU,CAAC,CAAC;EACvE,MAAM;IAACgD;EAAY,CAAC,GAAG7C,cAAc,CAAC8C,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAC1D;EACAnD,eAAe,CAAC,CAAC;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMoD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACA,MAAMN,UAAU,CAACR,MAAM,CAAC;MACxB1C,KAAK,CAACyD,OAAO,CAAC,uBAAuBJ,YAAY,EAAE,CAAC;IACxD,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C1D,KAAK,CAAC0D,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACJ,CAAC;EAED,MAAME,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACA,MAAMT,gBAAgB,CAACT,MAAM,CAAC;MAC9B1C,KAAK,CAACyD,OAAO,CAAC,4DAA4D,CAAC;IAC/E,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD1D,KAAK,CAAC0D,KAAK,CAAC,gEAAgE,CAAC;IACjF;EACJ,CAAC;EAED,MAAMG,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACAZ,QAAQ,CAACa,GAAG,CAAC;MACb,MAAMb,QAAQ,CAAC3C,YAAY,CAAC,CAAC,CAAC;MAC9BN,KAAK,CAACyD,OAAO,CAAC,4DAA4D,CAAC;IAC/E,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD1D,KAAK,CAAC0D,KAAK,CAAC,gEAAgE,CAAC;IACjF;EACJ,CAAC;EAED,MAAMK,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACA,MAAMX,iBAAiB,CAACV,MAAM,CAAC;MAC/B1C,KAAK,CAACyD,OAAO,CAAC,8CAA8C,CAAC;IACjE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD1D,KAAK,CAAC0D,KAAK,CAAC,mDAAmD,CAAC;IACpE;EACJ,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,IAAIrB,YAAY,EAAE;QACd;QACA,MAAMtD,IAAI,CAAC4E,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;UAAEC,OAAO,EAAExB;QAAO,CAAC,CAAC;QACjEE,eAAe,CAAC,KAAK,CAAC;QACtB5C,KAAK,CAACyD,OAAO,CAAC,gBAAgB,CAAC;MACnC,CAAC,MAAM;QACH;QACA,MAAMpE,IAAI,CAAC4E,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;UACzCC,OAAO,EAAExB,MAAM;UACfyB,YAAY,EAAEd;QAClB,CAAC,CAAC;QACFT,eAAe,CAAC,IAAI,CAAC;QACrB5C,KAAK,CAACyD,OAAO,CAAC,6BAA6BJ,YAAY,EAAE,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C1D,KAAK,CAAC0D,KAAK,CAAC,oCAAoC,CAAC;IACrD;EACJ,CAAC;EAED,MAAMU,sBAAsB,GAAIC,MAA8B,IAAK;IAC/D5C,eAAe,CAAC4C,MAAM,CAAC;IACvBrE,KAAK,CAACyD,OAAO,CAAC,0BAA0B,CAAC;EAC7C,CAAC;EAED3E,SAAS,CAAC,MAAM;IACZ6C,sBAAsB,CAAC,CAAC,CAAC;IACzB;IACAiB,eAAe,CAAC,KAAK,CAAC;IACtB;IACAvD,IAAI,CAAC4E,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;MAAEC,OAAO,EAAExB;IAAO,CAAC,CAAC,CAAC4B,KAAK,CAACX,OAAO,CAACD,KAAK,CAAC;EACpF,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EAElB,oBACI3C,OAAA;IAAK6D,SAAS,EAAC,kGAAkG;IAAAC,QAAA,gBAG7G9D,OAAA,CAACT,mBAAmB;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvBlE,OAAA;MAAK6D,SAAS,EAAC,kGAAkG;MAAAC,QAAA,gBAC7G9D,OAAA;QAAK6D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxC9D,OAAA;UACImE,OAAO,EAAEA,CAAA,KAAM/B,iBAAiB,CAAC,IAAI,CAAE;UACvCyB,SAAS,EAAC,kNAAkN;UAC5NO,KAAK,EAAC,+BAAgB;UAAAN,QAAA,gBAEtB9D,OAAA,CAACX,sBAAsB;YAACwE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC9B,EAACnB,YAAY,EAAC,KAAG,EAACA,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGA,YAAY,KAAK,GAAG,GAAG,WAAW,GAAGA,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGA,YAAY,KAAK,GAAG,GAAG,YAAY,GAAG,WAAW;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAU9C9D,OAAA;QAAK6D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GAEnCnB,YAAY,KAAK,GAAG,iBACjB3C,OAAA;UACIqE,GAAG,EAAE,CAAE;UACPC,KAAK,EAAEtD,mBAAoB;UAC3BuD,QAAQ,EAAEC,CAAC,IAAI;YACX,MAAMC,GAAG,GAAGD,CAAC,CAACE,MAAM,CAACJ,KAAK;YAC1B,IAAIG,GAAG,KAAK,EAAE,EAAE;cACZxD,sBAAsB,CAAC,CAAC,CAAC;YAC7B,CAAC,MAAM;cACHA,sBAAsB,CAAC0D,MAAM,CAACF,GAAG,CAAC,CAAC;YACvC;UACJ,CAAE;UACFZ,SAAS,EAAC,oJAAoJ;UAC9Je,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAE;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACJ,EACAvB,YAAY,KAAK,GAAG,iBACjB3C,OAAA;UACAmE,OAAO,EAAE,MAAAA,CAAA,KAAY;YACjB,IAAI;cACAvD,uBAAuB,CAACI,mBAAmB,CAAC8D,QAAQ,CAAC,CAAC,CAAC;;cAEvD;cACA,IAAInC,YAAY,KAAK,GAAG,EAAE;gBACtB,MAAMxC,kBAAkB,CAACgB,aAAa,EAAE4D,SAAS,EAAE/D,mBAAmB,CAAC8D,QAAQ,CAAC,CAAC,CAAC;cACtF,CAAC,MAAM,IAAInC,YAAY,KAAK,GAAG,EAAE;gBAC7B,MAAMxC,kBAAkB,CAAC4E,SAAS,EAAErD,KAAK,EAAEV,mBAAmB,CAAC8D,QAAQ,CAAC,CAAC,CAAC;cAC9E,CAAC,MAAM;gBACH,MAAM3E,kBAAkB,CAAC4E,SAAS,EAAEA,SAAS,EAAE/D,mBAAmB,CAAC8D,QAAQ,CAAC,CAAC,CAAC;cAClF;cAEAxF,KAAK,CAACyD,OAAO,CAAC,6BAA6B/B,mBAAmB,EAAE,CAAC;YACrE,CAAC,CAAC,OAAOgC,KAAK,EAAE;cACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;cAClD1D,KAAK,CAAC0D,KAAK,CAAC,6BAA6B,CAAC;YAC9C;UACJ,CAAE;UACFa,SAAS,EAAC,uQAAuQ;UAAAC,QAAA,gBAE7Q9D,OAAA,CAACnB,oBAAoB;YAACgF,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6CAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAENlE,OAAA;QAAK6D,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpC9D,OAAA;UACAmE,OAAO,EAAErB,qBAAsB;UAC/Be,SAAS,EAAC,kQAAkQ;UAAAC,QAAA,gBAE5Q9D,OAAA,CAAChB,cAAc;YAAC6E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sCAG/C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGJ,CAAC,eACNlE,OAAA;QACQmE,OAAO,EAAEhB,uBAAwB;QACjCU,SAAS,EAAC,2PAA2P;QAAAC,QAAA,gBAErQ9D,OAAA,CAACnB,oBAAoB;UAACgF,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA;QACAmE,OAAO,EAAEA,CAAA,KAAM;UACX9D,eAAe,CAAC,CAAC;UACjBf,KAAK,CAACyD,OAAO,CAAC,qBAAqB,CAAC;QACxC,CAAE;QACFc,SAAS,EAAC,oQAAoQ;QAAAC,QAAA,gBAE9Q9D,OAAA,CAACjB,SAAS;UAAC8E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iDAG1C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA;QACImE,OAAO,EAAEjB,2BAA4B;QACrCW,SAAS,EAAC,kQAAkQ;QAAAC,QAAA,gBAE5Q9D,OAAA,CAAClB,OAAO;UAAC+E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kDAGxC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAER,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC7C9D,OAAA;QACGmE,OAAO,EAAEd,uBAAwB;QACjCQ,SAAS,EAAC,0PAA0P;QAAAC,QAAA,gBAEpQ9D,OAAA,CAACpB,eAAe;UAACiF,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4CAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBACzC9D,OAAA;QACImE,OAAO,EAAEA,CAAA,KAAM;UACXzF,SAAS,CAACsD,MAAM,EAAEW,YAAY,CAAC;UAC/BrD,KAAK,CAACyD,OAAO,CAAC,iCAAiCJ,YAAY,EAAE,CAAC;QAClE,CAAE;QACFkB,SAAS,EAAC,uQAAuQ;QAAAC,QAAA,gBAEjR9D,OAAA,CAACf,eAAe;UAAC4E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4DAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA;QACImE,OAAO,EAAEA,CAAA,KAAM;UACXzF,SAAS,CAACsD,MAAM,EAAE,SAAS,CAAC;UAC5B1C,KAAK,CAACyD,OAAO,CAAC,0BAA0B,CAAC;QAC7C,CAAE;QACFc,SAAS,EAAC,gQAAgQ;QAAAC,QAAA,gBAE1Q9D,OAAA,CAACd,eAAe;UAAC2E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+CAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA;QACImE,OAAO,EAAEb,iBAAkB;QAC3BO,SAAS,EAAE,qBACP5B,YAAY,GACN,gGAAgG,GAChG,kGAAkG,wIAC6B;QAAA6B,QAAA,GAExI7B,YAAY,gBACTjC,OAAA,CAACZ,YAAY;UAACyE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzClE,OAAA,CAACb,gBAAgB;UAAC0E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC/C,EACAjC,YAAY,GAAG,aAAa,GAAG,mBAAmB;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGNlE,OAAA,CAACR,cAAc;MACXwF,MAAM,EAAE7C,cAAe;MACvB8C,OAAO,EAAEA,CAAA,KAAM7C,iBAAiB,CAAC,KAAK,CAAE;MACxC8C,KAAK,EAAEvC;IAAa;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eAEFlE,OAAA,CAACP,mBAAmB;MAChBuF,MAAM,EAAE3C,iBAAkB;MAC1B4C,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC,KAAK,CAAE;MAC3C6C,OAAO,EAAE1E,YAAa;MACtB2E,YAAY,EAAE1B,sBAAuB;MACrC2B,aAAa,EAAEvE;IAAa;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAChE,EAAA,CAvUID,cAAc;EAAA,QAiBZ3B,OAAO,EAEuLG,SAAS,EAEpLF,eAAe,EACrBC,WAAW,EASXqB,cAAc,EAE4BF,UAAU,EAC9CG,cAAc,EAErCJ,eAAe;AAAA;AAAA4F,EAAA,GApCbrF,cAAc;AAyUpB,eAAeA,cAAc;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}