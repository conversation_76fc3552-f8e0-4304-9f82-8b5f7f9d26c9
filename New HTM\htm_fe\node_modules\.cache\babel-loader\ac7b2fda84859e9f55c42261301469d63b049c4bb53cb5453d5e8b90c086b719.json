{"ast": null, "code": "// Game Redux slice\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport apiClient from '../../../shared/services/api/client';\nimport gameApi from '../../../shared/services/game/gameApi';\n\n// Initial state\nconst initialState = {\n  // Current game status\n  currentRound: \"1\",\n  currentTestName: \"\",\n  isActive: false,\n  isHost: false,\n  // Questions and answers\n  currentQuestion: null,\n  selectedPacketName: \"\",\n  questions: [],\n  currentCorrectAnswer: \"\",\n  // Players and scoring\n  players: [],\n  currentPlayer: null,\n  scores: [],\n  scoreRules: null,\n  // Round-specific data\n  round2Grid: null,\n  round4Grid: null,\n  // Game settings\n  mode: 'manual',\n  timeLimit: 30,\n  // UI state\n  showRules: false,\n  currentTurn: 0,\n  currentQuestionNumber: 1,\n  isBuzzOpen: false,\n  // Loading states\n  loading: {\n    isLoading: false,\n    error: null\n  },\n  // joing states\n  joining: {\n    isLoading: false,\n    error: null\n  },\n  //input disabled\n  isInputDisabled: true\n};\nexport const joinRoom = createAsyncThunk('room/joinRoom', async (joinData, {\n  rejectWithValue\n}) => {\n  try {\n    const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);\n    url.searchParams.append('room_id', joinData.roomId);\n    if (joinData.password) {\n      url.searchParams.append('password', joinData.password);\n    }\n    const response = await apiClient.post(url.toString(), joinData, {\n      _isAuthRequired: true\n    });\n    return response.data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n// Async thunks\nexport const getQuestions = createAsyncThunk('game/fetchQuestions', async (_, {\n  rejectWithValue,\n  getState\n}) => {\n  try {\n    const state = getState();\n    const {\n      currentTestName,\n      currentQuestionNumber,\n      currentRound,\n      selectedPacketName\n    } = state.game;\n    const nextQuestion = {\n      testName: currentTestName,\n      questionNumber: currentQuestionNumber,\n      round: currentRound,\n      packetName: currentRound === \"3\" ? selectedPacketName : undefined\n    };\n    const question = await gameApi.getQuestions(nextQuestion);\n    return question;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const submitAnswer = createAsyncThunk('game/submitAnswer', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/answer', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to submit answer');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const updateScores = createAsyncThunk('game/updateScores', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/scoring', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to update scores');\n    }\n    const data = await response.json();\n    return data.scores;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n\n// Game slice\nconst gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Game state management\n    setCurrentRound: (state, action) => {\n      state.currentRound = action.payload;\n      state.currentQuestionNumber = 1; // Reset question number when round changes\n    },\n    setCurrentTestName: (state, action) => {\n      state.currentTestName = action.payload;\n    },\n    setSelectedPacketName: (state, action) => {\n      state.selectedPacketName = action.payload;\n    },\n    setIsInputDisabled: (state, action) => {\n      state.isInputDisabled = action.payload;\n    },\n    setIsActive: (state, action) => {\n      state.isActive = action.payload;\n    },\n    setIsHost: (state, action) => {\n      state.isHost = action.payload;\n    },\n    // Question management\n    setCurrentQuestion: (state, action) => {\n      state.currentQuestion = action.payload;\n    },\n    setQuestions: (state, action) => {\n      state.questions = action.payload;\n    },\n    setCurrentCorrectAnswer: (state, action) => {\n      state.currentCorrectAnswer = action.payload;\n    },\n    setPlayerAnswerList: (state, action) => {\n      state.players = state.players.map(player => {\n        const answerUpdate = action.payload.find(a => a.uid === player.uid);\n        return answerUpdate ? {\n          ...player,\n          ...answerUpdate\n        } : player;\n      });\n    },\n    clearPlayerAnswerList: state => {\n      state.players = state.players.map(player => ({\n        ...player,\n        answer: \"\",\n        time: 0\n      }));\n    },\n    nextQuestion: state => {\n      state.currentQuestionNumber += 1;\n    },\n    setCurrentQuestionNumber: (state, action) => {\n      state.currentQuestionNumber = action.payload;\n    },\n    // Player management\n    setPlayers: (state, action) => {\n      state.players = state.players.map(player => {\n        const update = action.payload.find(p => p && p.uid === player.uid);\n        return update ? {\n          ...player,\n          ...update\n        } : player;\n      });\n    },\n    setCurrentPlayer: (state, action) => {\n      state.currentPlayer = action.payload;\n    },\n    updatePlayer: (state, action) => {\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (playerIndex !== -1) {\n        state.players[playerIndex] = {\n          ...state.players[playerIndex],\n          ...action.payload.updates\n        };\n      }\n    },\n    addPlayer: (state, action) => {\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (existingIndex === -1) {\n        state.players.push(action.payload);\n      }\n    },\n    removePlayer: (state, action) => {\n      state.players = state.players.filter(p => p.uid !== action.payload);\n    },\n    setPlayerAnswer: (state, action) => {\n      if (state.currentPlayer) {\n        state.currentPlayer.answer = action.payload.answer;\n        state.currentPlayer.time = action.payload.time;\n      }\n    },\n    // Scoring\n    setScores: (state, action) => {\n      state.scores = action.payload;\n    },\n    setScoreRules: (state, action) => {\n      state.scoreRules = action.payload;\n    },\n    // Round-specific data\n    setRound2Grid: (state, action) => {\n      if (action.payload === null) {\n        state.round2Grid = null;\n      } else {\n        state.round2Grid = {\n          ...(state.round2Grid || {}),\n          ...action.payload\n        };\n      }\n    },\n    setRound4Grid: (state, action) => {\n      if (action.payload === null) {\n        state.round4Grid = null;\n      } else {\n        state.round4Grid = {\n          ...(state.round4Grid || {}),\n          ...action.payload\n        };\n      }\n    },\n    // Game settings\n    setMode: (state, action) => {\n      state.mode = action.payload;\n    },\n    setTimeLimit: (state, action) => {\n      state.timeLimit = action.payload;\n    },\n    // UI state\n    setShowRules: (state, action) => {\n      state.showRules = action.payload;\n    },\n    setCurrentTurn: (state, action) => {\n      state.currentTurn = action.payload;\n    },\n    setIsBuzzOpen: (state, action) => {\n      state.isBuzzOpen = action.payload;\n    },\n    // Reset game state\n    resetGame: state => {\n      return {\n        ...initialState,\n        isHost: state.isHost\n      };\n    },\n    // Error handling\n    clearError: state => {\n      state.loading.error = null;\n    }\n  },\n  extraReducers: builder => {\n    // Join room\n    builder.addCase(joinRoom.pending, state => {\n      state.joining.isLoading = true;\n      state.joining.error = null;\n    }).addCase(joinRoom.fulfilled, (state, action) => {\n      state.joining.isLoading = false;\n      state.players = action.payload.players;\n      state.currentPlayer = {\n        ...state.currentPlayer,\n        ...action.meta.arg,\n        uid: action.payload.uid\n      };\n      state.isHost = false;\n    }).addCase(joinRoom.rejected, (state, action) => {\n      state.joining.isLoading = false;\n      state.joining.error = action.payload;\n    });\n    // Fetch questions\n    builder.addCase(getQuestions.pending, state => {\n      state.loading.isLoading = true;\n      state.loading.error = null;\n    }).addCase(getQuestions.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      state.currentQuestion = action.payload;\n      state.currentCorrectAnswer = action.payload.answer;\n    }).addCase(getQuestions.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Submit answer\n    builder.addCase(submitAnswer.pending, state => {\n      state.loading.isLoading = true;\n    }).addCase(submitAnswer.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      // Handle answer submission result\n    }).addCase(submitAnswer.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Update scores\n    builder.addCase(updateScores.fulfilled, (state, action) => {\n      state.scores = action.payload;\n    }).addCase(updateScores.rejected, (state, action) => {\n      state.loading.error = action.payload;\n    });\n  }\n});\nexport const {\n  setCurrentRound,\n  setIsActive,\n  setIsHost,\n  setCurrentQuestion,\n  setQuestions,\n  setCurrentCorrectAnswer,\n  nextQuestion,\n  setCurrentQuestionNumber,\n  setPlayers,\n  setCurrentPlayer,\n  setPlayerAnswer,\n  setPlayerAnswerList,\n  clearPlayerAnswerList,\n  updatePlayer,\n  addPlayer,\n  removePlayer,\n  setScores,\n  setScoreRules,\n  setRound2Grid,\n  setRound4Grid,\n  setMode,\n  setTimeLimit,\n  setShowRules,\n  setCurrentTurn,\n  resetGame,\n  clearError,\n  setIsInputDisabled,\n  setIsBuzzOpen\n} = gameSlice.actions;\nexport default gameSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "apiClient", "gameApi", "initialState", "currentRound", "currentTestName", "isActive", "isHost", "currentQuestion", "selectedPacketName", "questions", "currentCorrectAnswer", "players", "currentPlayer", "scores", "scoreRules", "round2Grid", "round4Grid", "mode", "timeLimit", "showRules", "currentTurn", "currentQuestionNumber", "isBuzzOpen", "loading", "isLoading", "error", "joining", "isInputDisabled", "joinRoom", "joinData", "rejectWithValue", "url", "URL", "process", "env", "REACT_APP_BASE_URL", "searchParams", "append", "roomId", "password", "response", "post", "toString", "_isAuthRequired", "data", "message", "getQuestions", "_", "getState", "state", "game", "nextQuestion", "testName", "questionNumber", "round", "packetName", "undefined", "question", "submitAnswer", "params", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "json", "updateScores", "gameSlice", "name", "reducers", "setCurrentRound", "action", "payload", "setCurrentTestName", "setSelectedPacketName", "setIsInputDisabled", "setIsActive", "setIsHost", "setCurrentQuestion", "setQuestions", "setCurrentCorrectAnswer", "setPlayerAnswerList", "map", "player", "answerUpdate", "find", "a", "uid", "clearPlayerAnswerList", "answer", "time", "setCurrentQuestionNumber", "setPlayers", "update", "p", "setCurrentPlayer", "updatePlayer", "playerIndex", "findIndex", "updates", "addPlayer", "existingIndex", "push", "removePlayer", "filter", "setPlayerAnswer", "setScores", "setScoreRules", "setRound2Grid", "setRound4Grid", "setMode", "setTimeLimit", "setShowRules", "setCurrentTurn", "setIsBuzzOpen", "resetGame", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "meta", "arg", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/gameSlice.ts"], "sourcesContent": ["// Game Redux slice\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { GameState, Question, Score, PlayerData, Round2Grid, Round4Grid, ScoreRule, RoomPlayer, JoinRoomRequest, Answer, GetQuestionsRequest } from '../../../shared/types';\r\nimport apiClient from '../../../shared/services/api/client';\r\nimport { set } from 'firebase/database';\r\nimport gameApi from '../../../shared/services/game/gameApi';\r\n\r\n// Initial state\r\nconst initialState: GameState = {\r\n  // Current game status\r\n  currentRound: \"1\",\r\n  currentTestName: \"\",\r\n  isActive: false,\r\n  isHost: false,\r\n\r\n  // Questions and answers\r\n  currentQuestion: null,\r\n  selectedPacketName: \"\",\r\n  questions: [],\r\n  currentCorrectAnswer: \"\",\r\n  // Players and scoring\r\n  players: [],\r\n  currentPlayer: null,\r\n  scores: [],\r\n  scoreRules: null,\r\n\r\n  // Round-specific data\r\n  round2Grid: null,\r\n  round4Grid: null,\r\n\r\n  // Game settings\r\n  mode: 'manual',\r\n  timeLimit: 30,\r\n\r\n  // UI state\r\n  showRules: false,\r\n  currentTurn: 0,\r\n  currentQuestionNumber: 1,\r\n  isBuzzOpen: false,\r\n\r\n  // Loading states\r\n  loading: {\r\n    isLoading: false,\r\n    error: null,\r\n  },\r\n\r\n  // joing states\r\n  joining: {\r\n    isLoading: false,\r\n    error: null,\r\n  },\r\n\r\n  //input disabled\r\n  isInputDisabled: true,\r\n};\r\n\r\n\r\nexport const joinRoom = createAsyncThunk(\r\n  'room/joinRoom',\r\n  async (joinData: JoinRoomRequest, { rejectWithValue }) => {\r\n    try {\r\n      const url = new URL('/api/room/join', process.env.REACT_APP_BASE_URL);\r\n      url.searchParams.append('room_id', joinData.roomId);\r\n      if (joinData.password) {\r\n        url.searchParams.append('password', joinData.password);\r\n      }\r\n      const response = await apiClient.post(url.toString(), joinData, { _isAuthRequired: true } as any);\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n// Async thunks\r\nexport const getQuestions = createAsyncThunk(\r\n  'game/fetchQuestions',\r\n  async (_, { rejectWithValue, getState }) => {\r\n    try {\r\n      const state = getState() as { game: GameState };\r\n\r\n      const { currentTestName, currentQuestionNumber, currentRound, selectedPacketName } = state.game;\r\n\r\n      const nextQuestion = {\r\n        testName: currentTestName,\r\n        questionNumber: currentQuestionNumber,\r\n        round: currentRound,\r\n        packetName: currentRound === \"3\" ? selectedPacketName : undefined\r\n      }\r\n      const question = await gameApi.getQuestions(nextQuestion);\r\n\r\n      return question;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const submitAnswer = createAsyncThunk(\r\n  'game/submitAnswer',\r\n  async (params: { roomId: string; uid: string; answer: string; time: number }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/game/answer', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to submit answer');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\nexport const updateScores = createAsyncThunk(\r\n  'game/updateScores',\r\n  async (params: { roomId: string; mode: string; scores?: Score[]; round: string }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Replace with actual API call\r\n      const response = await fetch('/api/game/scoring', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(params),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to update scores');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data.scores;\r\n    } catch (error: any) {\r\n      return rejectWithValue(error.message);\r\n    }\r\n  }\r\n);\r\n\r\n// Game slice\r\nconst gameSlice = createSlice({\r\n  name: 'game',\r\n  initialState,\r\n  reducers: {\r\n    // Game state management\r\n    setCurrentRound: (state, action: PayloadAction<string>) => {\r\n      state.currentRound = action.payload;\r\n      state.currentQuestionNumber = 1; // Reset question number when round changes\r\n    },\r\n\r\n    setCurrentTestName: (state, action: PayloadAction<string>) => {\r\n      state.currentTestName = action.payload;\r\n    },\r\n\r\n    setSelectedPacketName: (state, action: PayloadAction<string>) => {\r\n      state.selectedPacketName = action.payload;\r\n    },\r\n\r\n    setIsInputDisabled: (state, action: PayloadAction<boolean>) => {\r\n      state.isInputDisabled = action.payload;\r\n    },\r\n\r\n    setIsActive: (state, action: PayloadAction<boolean>) => {\r\n      state.isActive = action.payload;\r\n    },\r\n\r\n    setIsHost: (state, action: PayloadAction<boolean>) => {\r\n      state.isHost = action.payload;\r\n    },\r\n\r\n    // Question management\r\n    setCurrentQuestion: (state, action: PayloadAction<Question | null>) => {\r\n      state.currentQuestion = action.payload;\r\n    },\r\n\r\n    setQuestions: (state, action: PayloadAction<Question[]>) => {\r\n      state.questions = action.payload;\r\n    },\r\n\r\n    setCurrentCorrectAnswer: (state, action: PayloadAction<string>) => {\r\n      state.currentCorrectAnswer = action.payload;\r\n    },\r\n\r\n    setPlayerAnswerList: (state, action: PayloadAction<Answer[]>) => {\r\n      state.players = state.players.map(player => {\r\n        const answerUpdate = action.payload.find(a => a.uid === player.uid);\r\n        return answerUpdate ? { ...player, ...answerUpdate } : player;\r\n      });\r\n    },\r\n\r\n    clearPlayerAnswerList: (state) => {\r\n      state.players = state.players.map(player => ({\r\n        ...player,\r\n        answer: \"\",\r\n        time: 0,\r\n      }));\r\n    },\r\n\r\n    nextQuestion: (state) => {\r\n      state.currentQuestionNumber += 1;\r\n    },\r\n\r\n    setCurrentQuestionNumber: (state, action: PayloadAction<number>) => {\r\n      state.currentQuestionNumber = action.payload;\r\n    },\r\n    // Player management\r\n    setPlayers: (state, action: PayloadAction<Partial<PlayerData[]>>) => {\r\n      state.players = state.players.map(player => {\r\n        const update = action.payload.find(p => p && p.uid === player.uid);\r\n        return update ? { ...player, ...update } : player;\r\n      });\r\n    },\r\n\r\n    setCurrentPlayer: (state, action: PayloadAction<RoomPlayer>) => {\r\n      state.currentPlayer = action.payload;\r\n    },\r\n\r\n    updatePlayer: (state, action: PayloadAction<{ uid: string; updates: Partial<PlayerData> }>) => {\r\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\r\n      if (playerIndex !== -1) {\r\n        state.players[playerIndex] = { ...state.players[playerIndex], ...action.payload.updates };\r\n      }\r\n    },\r\n\r\n    addPlayer: (state, action: PayloadAction<PlayerData>) => {\r\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\r\n      if (existingIndex === -1) {\r\n        state.players.push(action.payload);\r\n      }\r\n    },\r\n\r\n    removePlayer: (state, action: PayloadAction<string>) => {\r\n      state.players = state.players.filter(p => p.uid !== action.payload);\r\n    },\r\n\r\n    setPlayerAnswer: (state, action: PayloadAction<{ answer: string; time: number }>) => {\r\n      if (state.currentPlayer) {\r\n        state.currentPlayer.answer = action.payload.answer;\r\n        state.currentPlayer.time = action.payload.time;\r\n      }\r\n    },\r\n\r\n    // Scoring\r\n    setScores: (state, action: PayloadAction<Score[]>) => {\r\n      state.scores = action.payload;\r\n    },\r\n\r\n    setScoreRules: (state, action: PayloadAction<ScoreRule>) => {\r\n      state.scoreRules = action.payload;\r\n    },\r\n\r\n    // Round-specific data\r\n    setRound2Grid: (state, action: PayloadAction<Partial<Round2Grid | null>>) => {\r\n      if (action.payload === null) {\r\n        state.round2Grid = null;\r\n      } else {\r\n        state.round2Grid = {\r\n          ...(state.round2Grid || {}),\r\n          ...action.payload,\r\n        };\r\n      }\r\n    },\r\n\r\n    setRound4Grid: (state, action: PayloadAction<Partial<Round4Grid | null>>) => {\r\n      if (action.payload === null) {\r\n        state.round4Grid = null;\r\n      } else {\r\n        state.round4Grid = {\r\n          ...(state.round4Grid || {}),\r\n          ...action.payload,\r\n        };\r\n      }\r\n    },\r\n\r\n    // Game settings\r\n    setMode: (state, action: PayloadAction<'manual' | 'auto' | 'adaptive'>) => {\r\n      state.mode = action.payload;\r\n    },\r\n\r\n    setTimeLimit: (state, action: PayloadAction<number>) => {\r\n      state.timeLimit = action.payload;\r\n    },\r\n\r\n    // UI state\r\n    setShowRules: (state, action: PayloadAction<boolean>) => {\r\n      state.showRules = action.payload;\r\n    },\r\n\r\n    setCurrentTurn: (state, action: PayloadAction<number>) => {\r\n      state.currentTurn = action.payload;\r\n    },\r\n\r\n    setIsBuzzOpen: (state, action: PayloadAction<boolean>) => {\r\n      state.isBuzzOpen = action.payload;\r\n    },\r\n\r\n    // Reset game state\r\n    resetGame: (state) => {\r\n      return { ...initialState, isHost: state.isHost };\r\n    },\r\n\r\n    // Error handling\r\n    clearError: (state) => {\r\n      state.loading.error = null;\r\n    },\r\n  },\r\n\r\n  extraReducers: (builder) => {\r\n    // Join room\r\n    builder\r\n      .addCase(joinRoom.pending, (state) => {\r\n        state.joining.isLoading = true;\r\n        state.joining.error = null;\r\n      })\r\n      .addCase(joinRoom.fulfilled, (state, action) => {\r\n        state.joining.isLoading = false;\r\n        state.players = action.payload.players;\r\n        state.currentPlayer = {\r\n          ...state.currentPlayer,\r\n          ...action.meta.arg,\r\n          uid: action.payload.uid\r\n        }\r\n        state.isHost = false;\r\n      })\r\n      .addCase(joinRoom.rejected, (state, action) => {\r\n        state.joining.isLoading = false;\r\n        state.joining.error = action.payload as string;\r\n      });\r\n    // Fetch questions\r\n    builder\r\n      .addCase(getQuestions.pending, (state) => {\r\n        state.loading.isLoading = true;\r\n        state.loading.error = null;\r\n      })\r\n      .addCase(getQuestions.fulfilled, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.currentQuestion = action.payload;\r\n        state.currentCorrectAnswer = action.payload.answer\r\n      })\r\n      .addCase(getQuestions.rejected, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.loading.error = action.payload as string;\r\n      });\r\n\r\n    // Submit answer\r\n    builder\r\n      .addCase(submitAnswer.pending, (state) => {\r\n        state.loading.isLoading = true;\r\n      })\r\n      .addCase(submitAnswer.fulfilled, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        // Handle answer submission result\r\n      })\r\n      .addCase(submitAnswer.rejected, (state, action) => {\r\n        state.loading.isLoading = false;\r\n        state.loading.error = action.payload as string;\r\n      });\r\n\r\n    // Update scores\r\n    builder\r\n      .addCase(updateScores.fulfilled, (state, action) => {\r\n        state.scores = action.payload;\r\n      })\r\n      .addCase(updateScores.rejected, (state, action) => {\r\n        state.loading.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const {\r\n  setCurrentRound,\r\n  setIsActive,\r\n  setIsHost,\r\n  setCurrentQuestion,\r\n  setQuestions,\r\n  setCurrentCorrectAnswer,\r\n  nextQuestion,\r\n  setCurrentQuestionNumber,\r\n  setPlayers,\r\n  setCurrentPlayer,\r\n  setPlayerAnswer,\r\n  setPlayerAnswerList,\r\n  clearPlayerAnswerList,\r\n  updatePlayer,\r\n  addPlayer,\r\n  removePlayer,\r\n  setScores,\r\n  setScoreRules,\r\n  setRound2Grid,\r\n  setRound4Grid,\r\n  setMode,\r\n  setTimeLimit,\r\n  setShowRules,\r\n  setCurrentTurn,\r\n  resetGame,\r\n  clearError,\r\n  setIsInputDisabled,\r\n  setIsBuzzOpen\r\n} = gameSlice.actions;\r\n\r\nexport default gameSlice.reducer;\r\n"], "mappings": "AAAA;AACA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAE/E,OAAOC,SAAS,MAAM,qCAAqC;AAE3D,OAAOC,OAAO,MAAM,uCAAuC;;AAE3D;AACA,MAAMC,YAAuB,GAAG;EAC9B;EACAC,YAAY,EAAE,GAAG;EACjBC,eAAe,EAAE,EAAE;EACnBC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,KAAK;EAEb;EACAC,eAAe,EAAE,IAAI;EACrBC,kBAAkB,EAAE,EAAE;EACtBC,SAAS,EAAE,EAAE;EACbC,oBAAoB,EAAE,EAAE;EACxB;EACAC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,EAAE;EACVC,UAAU,EAAE,IAAI;EAEhB;EACAC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAEhB;EACAC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,EAAE;EAEb;EACAC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,CAAC;EACdC,qBAAqB,EAAE,CAAC;EACxBC,UAAU,EAAE,KAAK;EAEjB;EACAC,OAAO,EAAE;IACPC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,OAAO,EAAE;IACPF,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAE,eAAe,EAAE;AACnB,CAAC;AAGD,OAAO,MAAMC,QAAQ,GAAG7B,gBAAgB,CACtC,eAAe,EACf,OAAO8B,QAAyB,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACxD,IAAI;IACF,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,gBAAgB,EAAEC,OAAO,CAACC,GAAG,CAACC,kBAAkB,CAAC;IACrEJ,GAAG,CAACK,YAAY,CAACC,MAAM,CAAC,SAAS,EAAER,QAAQ,CAACS,MAAM,CAAC;IACnD,IAAIT,QAAQ,CAACU,QAAQ,EAAE;MACrBR,GAAG,CAACK,YAAY,CAACC,MAAM,CAAC,UAAU,EAAER,QAAQ,CAACU,QAAQ,CAAC;IACxD;IACA,MAAMC,QAAQ,GAAG,MAAMxC,SAAS,CAACyC,IAAI,CAACV,GAAG,CAACW,QAAQ,CAAC,CAAC,EAAEb,QAAQ,EAAE;MAAEc,eAAe,EAAE;IAAK,CAAQ,CAAC;IAEjG,OAAOH,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOnB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AACD;AACA,OAAO,MAAMC,YAAY,GAAG/C,gBAAgB,CAC1C,qBAAqB,EACrB,OAAOgD,CAAC,EAAE;EAAEjB,eAAe;EAAEkB;AAAS,CAAC,KAAK;EAC1C,IAAI;IACF,MAAMC,KAAK,GAAGD,QAAQ,CAAC,CAAwB;IAE/C,MAAM;MAAE5C,eAAe;MAAEiB,qBAAqB;MAAElB,YAAY;MAAEK;IAAmB,CAAC,GAAGyC,KAAK,CAACC,IAAI;IAE/F,MAAMC,YAAY,GAAG;MACnBC,QAAQ,EAAEhD,eAAe;MACzBiD,cAAc,EAAEhC,qBAAqB;MACrCiC,KAAK,EAAEnD,YAAY;MACnBoD,UAAU,EAAEpD,YAAY,KAAK,GAAG,GAAGK,kBAAkB,GAAGgD;IAC1D,CAAC;IACD,MAAMC,QAAQ,GAAG,MAAMxD,OAAO,CAAC6C,YAAY,CAACK,YAAY,CAAC;IAEzD,OAAOM,QAAQ;EACjB,CAAC,CAAC,OAAOhC,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMa,YAAY,GAAG3D,gBAAgB,CAC1C,mBAAmB,EACnB,OAAO4D,MAAqE,EAAE;EAAE7B;AAAgB,CAAC,KAAK;EACpG,IAAI;IACF;IACA,MAAMU,QAAQ,GAAG,MAAMoB,KAAK,CAAC,kBAAkB,EAAE;MAC/CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACnB,QAAQ,CAAC0B,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMvB,IAAI,GAAG,MAAMJ,QAAQ,CAAC4B,IAAI,CAAC,CAAC;IAClC,OAAOxB,IAAI;EACb,CAAC,CAAC,OAAOnB,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMwB,YAAY,GAAGtE,gBAAgB,CAC1C,mBAAmB,EACnB,OAAO4D,MAAyE,EAAE;EAAE7B;AAAgB,CAAC,KAAK;EACxG,IAAI;IACF;IACA,MAAMU,QAAQ,GAAG,MAAMoB,KAAK,CAAC,mBAAmB,EAAE;MAChDC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACnB,QAAQ,CAAC0B,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMvB,IAAI,GAAG,MAAMJ,QAAQ,CAAC4B,IAAI,CAAC,CAAC;IAClC,OAAOxB,IAAI,CAAC/B,MAAM;EACpB,CAAC,CAAC,OAAOY,KAAU,EAAE;IACnB,OAAOK,eAAe,CAACL,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;;AAED;AACA,MAAMyB,SAAS,GAAGxE,WAAW,CAAC;EAC5ByE,IAAI,EAAE,MAAM;EACZrE,YAAY;EACZsE,QAAQ,EAAE;IACR;IACAC,eAAe,EAAEA,CAACxB,KAAK,EAAEyB,MAA6B,KAAK;MACzDzB,KAAK,CAAC9C,YAAY,GAAGuE,MAAM,CAACC,OAAO;MACnC1B,KAAK,CAAC5B,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAEDuD,kBAAkB,EAAEA,CAAC3B,KAAK,EAAEyB,MAA6B,KAAK;MAC5DzB,KAAK,CAAC7C,eAAe,GAAGsE,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDE,qBAAqB,EAAEA,CAAC5B,KAAK,EAAEyB,MAA6B,KAAK;MAC/DzB,KAAK,CAACzC,kBAAkB,GAAGkE,MAAM,CAACC,OAAO;IAC3C,CAAC;IAEDG,kBAAkB,EAAEA,CAAC7B,KAAK,EAAEyB,MAA8B,KAAK;MAC7DzB,KAAK,CAACtB,eAAe,GAAG+C,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDI,WAAW,EAAEA,CAAC9B,KAAK,EAAEyB,MAA8B,KAAK;MACtDzB,KAAK,CAAC5C,QAAQ,GAAGqE,MAAM,CAACC,OAAO;IACjC,CAAC;IAEDK,SAAS,EAAEA,CAAC/B,KAAK,EAAEyB,MAA8B,KAAK;MACpDzB,KAAK,CAAC3C,MAAM,GAAGoE,MAAM,CAACC,OAAO;IAC/B,CAAC;IAED;IACAM,kBAAkB,EAAEA,CAAChC,KAAK,EAAEyB,MAAsC,KAAK;MACrEzB,KAAK,CAAC1C,eAAe,GAAGmE,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDO,YAAY,EAAEA,CAACjC,KAAK,EAAEyB,MAAiC,KAAK;MAC1DzB,KAAK,CAACxC,SAAS,GAAGiE,MAAM,CAACC,OAAO;IAClC,CAAC;IAEDQ,uBAAuB,EAAEA,CAAClC,KAAK,EAAEyB,MAA6B,KAAK;MACjEzB,KAAK,CAACvC,oBAAoB,GAAGgE,MAAM,CAACC,OAAO;IAC7C,CAAC;IAEDS,mBAAmB,EAAEA,CAACnC,KAAK,EAAEyB,MAA+B,KAAK;MAC/DzB,KAAK,CAACtC,OAAO,GAAGsC,KAAK,CAACtC,OAAO,CAAC0E,GAAG,CAACC,MAAM,IAAI;QAC1C,MAAMC,YAAY,GAAGb,MAAM,CAACC,OAAO,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKJ,MAAM,CAACI,GAAG,CAAC;QACnE,OAAOH,YAAY,GAAG;UAAE,GAAGD,MAAM;UAAE,GAAGC;QAAa,CAAC,GAAGD,MAAM;MAC/D,CAAC,CAAC;IACJ,CAAC;IAEDK,qBAAqB,EAAG1C,KAAK,IAAK;MAChCA,KAAK,CAACtC,OAAO,GAAGsC,KAAK,CAACtC,OAAO,CAAC0E,GAAG,CAACC,MAAM,KAAK;QAC3C,GAAGA,MAAM;QACTM,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC;IAED1C,YAAY,EAAGF,KAAK,IAAK;MACvBA,KAAK,CAAC5B,qBAAqB,IAAI,CAAC;IAClC,CAAC;IAEDyE,wBAAwB,EAAEA,CAAC7C,KAAK,EAAEyB,MAA6B,KAAK;MAClEzB,KAAK,CAAC5B,qBAAqB,GAAGqD,MAAM,CAACC,OAAO;IAC9C,CAAC;IACD;IACAoB,UAAU,EAAEA,CAAC9C,KAAK,EAAEyB,MAA4C,KAAK;MACnEzB,KAAK,CAACtC,OAAO,GAAGsC,KAAK,CAACtC,OAAO,CAAC0E,GAAG,CAACC,MAAM,IAAI;QAC1C,MAAMU,MAAM,GAAGtB,MAAM,CAACC,OAAO,CAACa,IAAI,CAACS,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKJ,MAAM,CAACI,GAAG,CAAC;QAClE,OAAOM,MAAM,GAAG;UAAE,GAAGV,MAAM;UAAE,GAAGU;QAAO,CAAC,GAAGV,MAAM;MACnD,CAAC,CAAC;IACJ,CAAC;IAEDY,gBAAgB,EAAEA,CAACjD,KAAK,EAAEyB,MAAiC,KAAK;MAC9DzB,KAAK,CAACrC,aAAa,GAAG8D,MAAM,CAACC,OAAO;IACtC,CAAC;IAEDwB,YAAY,EAAEA,CAAClD,KAAK,EAAEyB,MAAoE,KAAK;MAC7F,MAAM0B,WAAW,GAAGnD,KAAK,CAACtC,OAAO,CAAC0F,SAAS,CAACJ,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAACe,GAAG,CAAC;MAC9E,IAAIU,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBnD,KAAK,CAACtC,OAAO,CAACyF,WAAW,CAAC,GAAG;UAAE,GAAGnD,KAAK,CAACtC,OAAO,CAACyF,WAAW,CAAC;UAAE,GAAG1B,MAAM,CAACC,OAAO,CAAC2B;QAAQ,CAAC;MAC3F;IACF,CAAC;IAEDC,SAAS,EAAEA,CAACtD,KAAK,EAAEyB,MAAiC,KAAK;MACvD,MAAM8B,aAAa,GAAGvD,KAAK,CAACtC,OAAO,CAAC0F,SAAS,CAACJ,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAACe,GAAG,CAAC;MAChF,IAAIc,aAAa,KAAK,CAAC,CAAC,EAAE;QACxBvD,KAAK,CAACtC,OAAO,CAAC8F,IAAI,CAAC/B,MAAM,CAACC,OAAO,CAAC;MACpC;IACF,CAAC;IAED+B,YAAY,EAAEA,CAACzD,KAAK,EAAEyB,MAA6B,KAAK;MACtDzB,KAAK,CAACtC,OAAO,GAAGsC,KAAK,CAACtC,OAAO,CAACgG,MAAM,CAACV,CAAC,IAAIA,CAAC,CAACP,GAAG,KAAKhB,MAAM,CAACC,OAAO,CAAC;IACrE,CAAC;IAEDiC,eAAe,EAAEA,CAAC3D,KAAK,EAAEyB,MAAuD,KAAK;MACnF,IAAIzB,KAAK,CAACrC,aAAa,EAAE;QACvBqC,KAAK,CAACrC,aAAa,CAACgF,MAAM,GAAGlB,MAAM,CAACC,OAAO,CAACiB,MAAM;QAClD3C,KAAK,CAACrC,aAAa,CAACiF,IAAI,GAAGnB,MAAM,CAACC,OAAO,CAACkB,IAAI;MAChD;IACF,CAAC;IAED;IACAgB,SAAS,EAAEA,CAAC5D,KAAK,EAAEyB,MAA8B,KAAK;MACpDzB,KAAK,CAACpC,MAAM,GAAG6D,MAAM,CAACC,OAAO;IAC/B,CAAC;IAEDmC,aAAa,EAAEA,CAAC7D,KAAK,EAAEyB,MAAgC,KAAK;MAC1DzB,KAAK,CAACnC,UAAU,GAAG4D,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACAoC,aAAa,EAAEA,CAAC9D,KAAK,EAAEyB,MAAiD,KAAK;MAC3E,IAAIA,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QAC3B1B,KAAK,CAAClC,UAAU,GAAG,IAAI;MACzB,CAAC,MAAM;QACLkC,KAAK,CAAClC,UAAU,GAAG;UACjB,IAAIkC,KAAK,CAAClC,UAAU,IAAI,CAAC,CAAC,CAAC;UAC3B,GAAG2D,MAAM,CAACC;QACZ,CAAC;MACH;IACF,CAAC;IAEDqC,aAAa,EAAEA,CAAC/D,KAAK,EAAEyB,MAAiD,KAAK;MAC3E,IAAIA,MAAM,CAACC,OAAO,KAAK,IAAI,EAAE;QAC3B1B,KAAK,CAACjC,UAAU,GAAG,IAAI;MACzB,CAAC,MAAM;QACLiC,KAAK,CAACjC,UAAU,GAAG;UACjB,IAAIiC,KAAK,CAACjC,UAAU,IAAI,CAAC,CAAC,CAAC;UAC3B,GAAG0D,MAAM,CAACC;QACZ,CAAC;MACH;IACF,CAAC;IAED;IACAsC,OAAO,EAAEA,CAAChE,KAAK,EAAEyB,MAAqD,KAAK;MACzEzB,KAAK,CAAChC,IAAI,GAAGyD,MAAM,CAACC,OAAO;IAC7B,CAAC;IAEDuC,YAAY,EAAEA,CAACjE,KAAK,EAAEyB,MAA6B,KAAK;MACtDzB,KAAK,CAAC/B,SAAS,GAAGwD,MAAM,CAACC,OAAO;IAClC,CAAC;IAED;IACAwC,YAAY,EAAEA,CAAClE,KAAK,EAAEyB,MAA8B,KAAK;MACvDzB,KAAK,CAAC9B,SAAS,GAAGuD,MAAM,CAACC,OAAO;IAClC,CAAC;IAEDyC,cAAc,EAAEA,CAACnE,KAAK,EAAEyB,MAA6B,KAAK;MACxDzB,KAAK,CAAC7B,WAAW,GAAGsD,MAAM,CAACC,OAAO;IACpC,CAAC;IAED0C,aAAa,EAAEA,CAACpE,KAAK,EAAEyB,MAA8B,KAAK;MACxDzB,KAAK,CAAC3B,UAAU,GAAGoD,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACA2C,SAAS,EAAGrE,KAAK,IAAK;MACpB,OAAO;QAAE,GAAG/C,YAAY;QAAEI,MAAM,EAAE2C,KAAK,CAAC3C;MAAO,CAAC;IAClD,CAAC;IAED;IACAiH,UAAU,EAAGtE,KAAK,IAAK;MACrBA,KAAK,CAAC1B,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B;EACF,CAAC;EAED+F,aAAa,EAAGC,OAAO,IAAK;IAC1B;IACAA,OAAO,CACJC,OAAO,CAAC9F,QAAQ,CAAC+F,OAAO,EAAG1E,KAAK,IAAK;MACpCA,KAAK,CAACvB,OAAO,CAACF,SAAS,GAAG,IAAI;MAC9ByB,KAAK,CAACvB,OAAO,CAACD,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDiG,OAAO,CAAC9F,QAAQ,CAACgG,SAAS,EAAE,CAAC3E,KAAK,EAAEyB,MAAM,KAAK;MAC9CzB,KAAK,CAACvB,OAAO,CAACF,SAAS,GAAG,KAAK;MAC/ByB,KAAK,CAACtC,OAAO,GAAG+D,MAAM,CAACC,OAAO,CAAChE,OAAO;MACtCsC,KAAK,CAACrC,aAAa,GAAG;QACpB,GAAGqC,KAAK,CAACrC,aAAa;QACtB,GAAG8D,MAAM,CAACmD,IAAI,CAACC,GAAG;QAClBpC,GAAG,EAAEhB,MAAM,CAACC,OAAO,CAACe;MACtB,CAAC;MACDzC,KAAK,CAAC3C,MAAM,GAAG,KAAK;IACtB,CAAC,CAAC,CACDoH,OAAO,CAAC9F,QAAQ,CAACmG,QAAQ,EAAE,CAAC9E,KAAK,EAAEyB,MAAM,KAAK;MAC7CzB,KAAK,CAACvB,OAAO,CAACF,SAAS,GAAG,KAAK;MAC/ByB,KAAK,CAACvB,OAAO,CAACD,KAAK,GAAGiD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;IACJ;IACA8C,OAAO,CACJC,OAAO,CAAC5E,YAAY,CAAC6E,OAAO,EAAG1E,KAAK,IAAK;MACxCA,KAAK,CAAC1B,OAAO,CAACC,SAAS,GAAG,IAAI;MAC9ByB,KAAK,CAAC1B,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACDiG,OAAO,CAAC5E,YAAY,CAAC8E,SAAS,EAAE,CAAC3E,KAAK,EAAEyB,MAAM,KAAK;MAClDzB,KAAK,CAAC1B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/ByB,KAAK,CAAC1C,eAAe,GAAGmE,MAAM,CAACC,OAAO;MACtC1B,KAAK,CAACvC,oBAAoB,GAAGgE,MAAM,CAACC,OAAO,CAACiB,MAAM;IACpD,CAAC,CAAC,CACD8B,OAAO,CAAC5E,YAAY,CAACiF,QAAQ,EAAE,CAAC9E,KAAK,EAAEyB,MAAM,KAAK;MACjDzB,KAAK,CAAC1B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/ByB,KAAK,CAAC1B,OAAO,CAACE,KAAK,GAAGiD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA8C,OAAO,CACJC,OAAO,CAAChE,YAAY,CAACiE,OAAO,EAAG1E,KAAK,IAAK;MACxCA,KAAK,CAAC1B,OAAO,CAACC,SAAS,GAAG,IAAI;IAChC,CAAC,CAAC,CACDkG,OAAO,CAAChE,YAAY,CAACkE,SAAS,EAAE,CAAC3E,KAAK,EAAEyB,MAAM,KAAK;MAClDzB,KAAK,CAAC1B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B;IACF,CAAC,CAAC,CACDkG,OAAO,CAAChE,YAAY,CAACqE,QAAQ,EAAE,CAAC9E,KAAK,EAAEyB,MAAM,KAAK;MACjDzB,KAAK,CAAC1B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/ByB,KAAK,CAAC1B,OAAO,CAACE,KAAK,GAAGiD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA8C,OAAO,CACJC,OAAO,CAACrD,YAAY,CAACuD,SAAS,EAAE,CAAC3E,KAAK,EAAEyB,MAAM,KAAK;MAClDzB,KAAK,CAACpC,MAAM,GAAG6D,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC,CACD+C,OAAO,CAACrD,YAAY,CAAC0D,QAAQ,EAAE,CAAC9E,KAAK,EAAEyB,MAAM,KAAK;MACjDzB,KAAK,CAAC1B,OAAO,CAACE,KAAK,GAAGiD,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXF,eAAe;EACfM,WAAW;EACXC,SAAS;EACTC,kBAAkB;EAClBC,YAAY;EACZC,uBAAuB;EACvBhC,YAAY;EACZ2C,wBAAwB;EACxBC,UAAU;EACVG,gBAAgB;EAChBU,eAAe;EACfxB,mBAAmB;EACnBO,qBAAqB;EACrBQ,YAAY;EACZI,SAAS;EACTG,YAAY;EACZG,SAAS;EACTC,aAAa;EACbC,aAAa;EACbC,aAAa;EACbC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,cAAc;EACdE,SAAS;EACTC,UAAU;EACVzC,kBAAkB;EAClBuC;AACF,CAAC,GAAG/C,SAAS,CAAC0D,OAAO;AAErB,eAAe1D,SAAS,CAAC2D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}