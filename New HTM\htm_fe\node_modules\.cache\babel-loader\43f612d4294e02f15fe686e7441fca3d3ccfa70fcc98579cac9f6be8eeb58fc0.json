{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\components\\\\HostManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useHost } from '../context/hostContext';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { usePlayer } from '../context/playerContext';\nimport { playSound } from './services';\nimport http from '../services/http';\nimport { CheckCircleIcon, ArrowRightCircleIcon, EyeIcon, ClockIcon, PlayCircleIcon, SpeakerWaveIcon, MusicalNoteIcon, DocumentTextIcon, EyeSlashIcon, QuestionMarkCircleIcon } from \"@heroicons/react/24/solid\";\nimport { toast } from 'react-toastify';\nimport HostQuestionPreview from './HostQuestionPreview';\nimport HostGuideModal from './HostGuideModal';\nimport PlayerColorSelector from './PlayerColorSelector';\nimport useTokenRefresh from '../hooks/useTokenRefresh';\nimport useGameApi from '../shared/hooks/api/useGameApi';\nimport { useAppDispatch } from '../app/store';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HostManagement = () => {\n  _s();\n  const {\n    handleNextQuestion,\n    handleShowAnswer,\n    handleStartTime,\n    handleStartRound,\n    handleCorrectAnswer,\n    currentAnswer,\n    playerScores,\n    setPlayerScores,\n    currentQuestionIndex,\n    setCurrentQuestionIndex,\n    hostInitialGrid,\n    playerColors,\n    setPlayerColors,\n    inGameQuestionIndex,\n    setInGameQuestionIndex\n  } = useHost();\n  const {\n    initialGrid,\n    selectedTopic,\n    easyQuestionNumber,\n    mediumQuestionNumber,\n    hardQuestionNumber,\n    setEasyQuestionNumber,\n    setMediumQuestionNumber,\n    setHardQuestionNumber,\n    level,\n    setAnswerList\n  } = usePlayer();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const currentRound = searchParams.get(\"round\") || \"1\";\n  const testName = searchParams.get(\"testName\") || \"1\";\n  const roomId = searchParams.get(\"roomId\") || \"1\";\n  const [showingRules, setShowingRules] = useState(false);\n  const [showGuideModal, setShowGuideModal] = useState(false);\n  const [showColorSelector, setShowColorSelector] = useState(false);\n  const dispatch = useAppDispatch();\n  const {\n    startRound,\n    broadcastAnswers,\n    sendCorrectAnswer\n  } = useGameApi();\n\n  // Initialize token refresh for host\n  useTokenRefresh();\n  // const handleRoundChange = async (delta: number) => {\n  //     console.log(\"currentRound\", currentRound)\n  //     const newRound = parseInt(currentRound) + delta;\n  //     console.log(\"new round\", newRound)\n  //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\n  //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\n  //     }\n\n  //     // Clear frontend state\n  //     setAnswerList([]);\n\n  //     // Clear Firebase data\n  //     await deletePath(roomId, \"questions\");\n  //     await deletePath(roomId, \"answers\");\n  //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\n  //     await deletePath(roomId, \"turn\"); // Clear turn assignments\n  //     await deletePath(roomId, \"isModified\"); // Clear isModified state\n  //     // Don't clear showRules here - let host control modal display manually\n  //     setShowingRules(false); // Reset rules button state\n  // };\n\n  const handleStartRoundClick = async () => {\n    try {\n      await startRound({\n        roomId,\n        round: currentRound,\n        grid: initialGrid\n      });\n      toast.success(`Đã bắt đầu vòng thi ${currentRound}`);\n    } catch (error) {\n      console.error('Error starting round:', error);\n      toast.error('Lỗi khi bắt đầu vòng thi');\n    }\n  };\n  const handleToggleRules = async () => {\n    try {\n      if (showingRules) {\n        // Hide rules\n        await http.post('game/rules/hide', true, {}, {\n          room_id: roomId\n        });\n        setShowingRules(false);\n        toast.success('Đã ẩn luật thi');\n      } else {\n        // Show rules\n        await http.post('room/rules/show', true, {}, {\n          room_id: roomId,\n          round_number: currentRound\n        });\n        setShowingRules(true);\n        toast.success(`Đã hiển thị luật thi vòng ${currentRound}`);\n      }\n    } catch (error) {\n      console.error('Error toggling rules:', error);\n      toast.error('Lỗi khi thay đổi hiển thị luật thi');\n    }\n  };\n  const handleSavePlayerColors = colors => {\n    setPlayerColors(colors);\n    toast.success('Đã lưu màu cho thí sinh!');\n  };\n  useEffect(() => {\n    setInGameQuestionIndex(1);\n    // Clear rules when entering new round to prevent auto-show\n    setShowingRules(false);\n    // Also clear rules from Firebase to ensure clean state\n    http.post('game/rules/hide', true, {}, {\n      room_id: roomId\n    }).catch(console.error);\n  }, [currentRound]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-4 lg:p-6 mt-4\",\n    children: [/*#__PURE__*/_jsxDEV(HostQuestionPreview, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowGuideModal(true),\n          className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 font-medium text-sm\",\n          title: \"H\\u01B0\\u1EDBng d\\u1EABn host\",\n          children: [/*#__PURE__*/_jsxDEV(QuestionMarkCircleIcon, {\n            className: \"w-5 h-5 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this), \"H\\u01B0\\u1EDBng d\\u1EABn\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-400 text-sm\",\n        children: [\"V\\xF2ng \", currentRound, \" - \", currentRound === \"1\" ? \"NHỔ NEO\" : currentRound === \"2\" ? \"VƯỢT SÓNG\" : currentRound === \"3\" ? \"BỨT PHÁ\" : currentRound === \"4\" ? \"CHINH PHỤC\" : \"PHÂN LƯỢT\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [currentRound !== \"4\" && /*#__PURE__*/_jsxDEV(\"input\", {\n          min: 0,\n          value: inGameQuestionIndex,\n          onChange: e => {\n            const val = e.target.value;\n            if (val === \"\") {\n              setInGameQuestionIndex(0);\n            } else {\n              setInGameQuestionIndex(Number(val));\n            }\n          },\n          className: \"w-16 px-2 py-2 rounded-lg border border-blue-400 bg-slate-700 text-white text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-400\",\n          style: {\n            minWidth: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 25\n        }, this), currentRound !== \"4\" && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: async () => {\n            try {\n              setCurrentQuestionIndex(inGameQuestionIndex.toString());\n\n              // Fetch and display the specified question\n              if (currentRound === \"3\") {\n                await handleNextQuestion(selectedTopic, undefined, inGameQuestionIndex.toString());\n              } else if (currentRound === \"4\") {\n                await handleNextQuestion(undefined, level, inGameQuestionIndex.toString());\n              } else {\n                await handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\n              }\n              toast.success(`Đã chuyển đến câu hỏi số: ${inGameQuestionIndex}`);\n            } catch (error) {\n              console.error(\"Error jumping to question:\", error);\n              toast.error(\"Lỗi khi chuyển đến câu hỏi!\");\n            }\n          },\n          className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowRightCircleIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 29\n          }, this), \"CHUY\\u1EC2N \\u0110\\u1EBEN C\\xC2U H\\u1ECEI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: han,\n          className: \"w-full flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base\",\n          children: [/*#__PURE__*/_jsxDEV(PlayCircleIcon, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 21\n          }, this), \"B\\u1EAET \\u0110\\u1EA6U V\\xD2NG THI\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          if (currentRound === \"3\") {\n            // Use the input box value for round 3\n            handleNextQuestion(selectedTopic, undefined, \"0\");\n            setInGameQuestionIndex(prev => prev + 1);\n            return;\n          }\n          if (currentRound === \"4\") {\n            if (level === \"Dễ\") {\n              handleNextQuestion(undefined, level, easyQuestionNumber.toString());\n              setEasyQuestionNumber(prev => prev + 1);\n            }\n            if (level === \"Trung bình\") {\n              handleNextQuestion(undefined, level, (20 + mediumQuestionNumber).toString());\n              setMediumQuestionNumber(prev => prev + 1);\n            }\n            if (level === \"Khó\") {\n              handleNextQuestion(undefined, level, (40 + hardQuestionNumber).toString());\n              setHardQuestionNumber(prev => prev + 1);\n            }\n            return;\n          }\n          // Use the input box value for other rounds\n          handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\n          setInGameQuestionIndex(prev => prev + 1);\n        },\n        className: \"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ArrowRightCircleIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 25\n        }, this), \"C\\xC2U H\\u1ECEI TI\\u1EBEP THEO\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          handleStartTime();\n          toast.success(\"Đã bắt đầu đếm giờ!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 21\n        }, this), \"B\\u1EAET \\u0110\\u1EA6U \\u0110\\u1EBEM GI\\u1EDC\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          handleShowAnswer();\n          toast.success(\"Đã hiển thị câu trả lời cho người chơi!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(EyeIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 21\n        }, this), \"HI\\u1EC6N C\\xC2U TR\\u1EA2 L\\u1EDCI TH\\xCD SINH\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handleCorrectAnswer(currentAnswer),\n        className: \"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 21\n        }, this), \"HI\\u1EC6N \\u0110\\xC1P \\xC1N \\u0110\\xDANG\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-3 lg:gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          playSound(roomId, currentRound);\n          toast.success(`Đã chạy âm thanh cho vòng thi ${currentRound}`);\n        },\n        className: \"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(SpeakerWaveIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 21\n        }, this), \"CH\\u1EA0Y \\xC2M THANH B\\u1EAET \\u0110\\u1EA6U V\\xD2NG THI\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          playSound(roomId, \"opening\");\n          toast.success(\"Đã chạy âm thanh mở đầu!\");\n        },\n        className: \"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\",\n        children: [/*#__PURE__*/_jsxDEV(MusicalNoteIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 21\n        }, this), \"CH\\u1EA0Y \\xC2M THANH M\\u1EDE \\u0110\\u1EA6U\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleToggleRules,\n        className: `flex items-center ${showingRules ? 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-red-400/50' : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-green-400/50'} text-white p-2 lg:p-3 rounded-lg shadow-md border transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full`,\n        children: [showingRules ? /*#__PURE__*/_jsxDEV(EyeSlashIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n          className: \"w-4 h-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 25\n        }, this), showingRules ? 'ẨN LUẬT THI' : 'HIỂN THỊ LUẬT THI']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(HostGuideModal, {\n      isOpen: showGuideModal,\n      onClose: () => setShowGuideModal(false),\n      round: currentRound\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(PlayerColorSelector, {\n      isOpen: showColorSelector,\n      onClose: () => setShowColorSelector(false),\n      players: playerScores,\n      onSaveColors: handleSavePlayerColors,\n      currentColors: playerColors\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 9\n  }, this);\n};\n_s(HostManagement, \"D/y9P05cRKpzRfEavmP0HM83EEA=\", false, function () {\n  return [useHost, usePlayer, useSearchParams, useNavigate, useAppDispatch, useGameApi, useTokenRefresh];\n});\n_c = HostManagement;\nexport default HostManagement;\nvar _c;\n$RefreshReg$(_c, \"HostManagement\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useHost", "useSearchParams", "useNavigate", "usePlayer", "playSound", "http", "CheckCircleIcon", "ArrowRightCircleIcon", "EyeIcon", "ClockIcon", "PlayCircleIcon", "SpeakerWaveIcon", "MusicalNoteIcon", "DocumentTextIcon", "EyeSlashIcon", "QuestionMarkCircleIcon", "toast", "HostQuestionPreview", "HostGuideModal", "PlayerColorSelector", "useTokenRefresh", "useGameApi", "useAppDispatch", "jsxDEV", "_jsxDEV", "HostManagement", "_s", "handleNextQuestion", "handleShowAnswer", "handleStartTime", "handleStartRound", "handleCorrectAnswer", "currentAnswer", "playerScores", "setPlayerScores", "currentQuestionIndex", "setCurrentQuestionIndex", "hostInitialGrid", "playerColors", "setPlayerColors", "inGameQuestionIndex", "setInGameQuestionIndex", "initialGrid", "selectedTopic", "easyQuestionNumber", "mediumQuestionNumber", "hardQuestionNumber", "setEasyQuestionNumber", "setMediumQuestionNumber", "setHardQuestionNumber", "level", "setAnswerList", "searchParams", "navigate", "currentRound", "get", "testName", "roomId", "showingRules", "setShowingRules", "showGuideModal", "setShowGuideModal", "showColorSelector", "setShowColorSelector", "dispatch", "startRound", "broadcastAnswers", "sendCorrectAnswer", "handleStartRoundClick", "round", "grid", "success", "error", "console", "handleToggleRules", "post", "room_id", "round_number", "handleSavePlayerColors", "colors", "catch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "min", "value", "onChange", "e", "val", "target", "Number", "style", "min<PERSON><PERSON><PERSON>", "toString", "undefined", "han", "prev", "isOpen", "onClose", "players", "onSaveColors", "currentColors", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/components/HostManagement.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react'\r\nimport { useHost } from '../context/hostContext';\r\nimport { useSearchParams, useNavigate } from 'react-router-dom';\r\nimport { usePlayer } from '../context/playerContext';\r\nimport { openBuzz } from './services';\r\nimport { playSound } from './services';\r\nimport { deletePath } from '../services/firebaseServices';\r\nimport { updateScore } from '../pages/Host/Management/service';\r\nimport http from '../services/http';\r\nimport {\r\n    CheckCircleIcon,\r\n    BellAlertIcon,\r\n    ArrowRightCircleIcon,\r\n    EyeIcon,\r\n    ClockIcon,\r\n    PlayCircleIcon,\r\n    SpeakerWaveIcon,\r\n    MusicalNoteIcon,\r\n    DocumentTextIcon,\r\n    EyeSlashIcon,\r\n    QuestionMarkCircleIcon,\r\n    PaintBrushIcon,\r\n} from \"@heroicons/react/24/solid\";\r\nimport { toast } from 'react-toastify';\r\nimport HostQuestionPreview from './HostQuestionPreview';\r\nimport HostGuideModal from './HostGuideModal';\r\nimport PlayerColorSelector from './PlayerColorSelector';\r\nimport useTokenRefresh from '../hooks/useTokenRefresh';\r\nimport useGameApi from '../shared/hooks/api/useGameApi';\r\nimport { getQuestions } from '../app/store/slices/gameSlice';\r\nimport { useAppDispatch } from '../app/store';\r\n\r\n\r\nconst HostManagement = () => {\r\n    const {\r\n        handleNextQuestion,\r\n        handleShowAnswer,\r\n        handleStartTime,\r\n        handleStartRound,\r\n        handleCorrectAnswer,\r\n        currentAnswer,\r\n        playerScores,\r\n        setPlayerScores,\r\n        currentQuestionIndex,\r\n        setCurrentQuestionIndex,\r\n        hostInitialGrid,\r\n        playerColors,\r\n        setPlayerColors,\r\n        inGameQuestionIndex,\r\n        setInGameQuestionIndex\r\n    } = useHost();\r\n\r\n    const { initialGrid, selectedTopic, easyQuestionNumber, mediumQuestionNumber, hardQuestionNumber, setEasyQuestionNumber, setMediumQuestionNumber, setHardQuestionNumber, level, setAnswerList } = usePlayer()\r\n\r\n    const [searchParams] = useSearchParams();\r\n    const navigate = useNavigate();\r\n\r\n    const currentRound = searchParams.get(\"round\") || \"1\";\r\n    const testName = searchParams.get(\"testName\") || \"1\"\r\n    const roomId = searchParams.get(\"roomId\") || \"1\"\r\n    const [showingRules, setShowingRules] = useState(false);\r\n    const [showGuideModal, setShowGuideModal] = useState(false);\r\n    const [showColorSelector, setShowColorSelector] = useState(false);\r\n\r\n    const dispatch = useAppDispatch();\r\n\r\n    const {startRound, broadcastAnswers, sendCorrectAnswer } = useGameApi()\r\n\r\n    // Initialize token refresh for host\r\n    useTokenRefresh();\r\n    // const handleRoundChange = async (delta: number) => {\r\n    //     console.log(\"currentRound\", currentRound)\r\n    //     const newRound = parseInt(currentRound) + delta;\r\n    //     console.log(\"new round\", newRound)\r\n    //     if (newRound >= 1 && newRound <= 4) { // limit to 1-4 rounds\r\n    //         navigate(`?round=${newRound}&testName=${testName}&roomId=${roomId}`);\r\n    //     }\r\n\r\n    //     // Clear frontend state\r\n    //     setAnswerList([]);\r\n\r\n    //     // Clear Firebase data\r\n    //     await deletePath(roomId, \"questions\");\r\n    //     await deletePath(roomId, \"answers\");\r\n    //     await deletePath(roomId, \"answerLists\"); // Clear answer lists\r\n    //     await deletePath(roomId, \"turn\"); // Clear turn assignments\r\n    //     await deletePath(roomId, \"isModified\"); // Clear isModified state\r\n    //     // Don't clear showRules here - let host control modal display manually\r\n    //     setShowingRules(false); // Reset rules button state\r\n    // };\r\n\r\n    const handleStartRoundClick = async () => {\r\n        try {\r\n            await startRound({ roomId, round: currentRound, grid: initialGrid });\r\n            toast.success(`Đã bắt đầu vòng thi ${currentRound}`);\r\n        } catch (error) {\r\n            console.error('Error starting round:', error);\r\n            toast.error('Lỗi khi bắt đầu vòng thi');\r\n        }\r\n    }\r\n\r\n    const handleToggleRules = async () => {\r\n        try {\r\n            if (showingRules) {\r\n                // Hide rules\r\n                await http.post('game/rules/hide', true, {}, { room_id: roomId });\r\n                setShowingRules(false);\r\n                toast.success('Đã ẩn luật thi');\r\n            } else {\r\n                // Show rules\r\n                await http.post('room/rules/show', true, {}, {\r\n                    room_id: roomId,\r\n                    round_number: currentRound\r\n                });\r\n                setShowingRules(true);\r\n                toast.success(`Đã hiển thị luật thi vòng ${currentRound}`);\r\n            }\r\n        } catch (error) {\r\n            console.error('Error toggling rules:', error);\r\n            toast.error('Lỗi khi thay đổi hiển thị luật thi');\r\n        }\r\n    };\r\n\r\n    const handleSavePlayerColors = (colors: Record<string, string>) => {\r\n        setPlayerColors(colors);\r\n        toast.success('Đã lưu màu cho thí sinh!');\r\n    };\r\n\r\n    useEffect(() => {\r\n        setInGameQuestionIndex(1);\r\n        // Clear rules when entering new round to prevent auto-show\r\n        setShowingRules(false);\r\n        // Also clear rules from Firebase to ensure clean state\r\n        http.post('game/rules/hide', true, {}, { room_id: roomId }).catch(console.error);\r\n    }, [currentRound]);\r\n\r\n    return (\r\n        <div className=\"bg-slate-800/80 backdrop-blur-sm rounded-xl border border-blue-400/30 shadow-2xl p-4 lg:p-6 mt-4\">\r\n\r\n            {/* Host Question Preview */}\r\n            <HostQuestionPreview />\r\n\r\n            {/* Guide and Color Selection */}\r\n            <div className=\"flex items-center justify-between mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                    <button\r\n                        onClick={() => setShowGuideModal(true)}\r\n                        className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 font-medium text-sm\"\r\n                        title=\"Hướng dẫn host\"\r\n                    >\r\n                        <QuestionMarkCircleIcon className=\"w-5 h-5 mr-2\" />\r\n                        Hướng dẫn\r\n                    </button>\r\n\r\n                </div>\r\n\r\n                <div className=\"text-gray-400 text-sm\">\r\n                    Vòng {currentRound} - {currentRound === \"1\" ? \"NHỔ NEO\" : currentRound === \"2\" ? \"VƯỢT SÓNG\" : currentRound === \"3\" ? \"BỨT PHÁ\" : currentRound === \"4\" ? \"CHINH PHỤC\" : \"PHÂN LƯỢT\"}\r\n                </div>\r\n            </div>\r\n\r\n            {/* Host actions - First row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4 mb-4\">\r\n\r\n                {/* <button\r\n                    onClick={() => openBuzz(roomId)}\r\n                    className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-3 lg:p-4 rounded-xl shadow-lg border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <BellAlertIcon className=\"w-5 h-5 mr-2\" />\r\n\r\n                    MỞ BẤM CHUÔNG\r\n                </button> */}\r\n                <div className=\"flex items-center gap-3\">\r\n                    {/* Current Question Index Input - Disabled for Round 4 */}\r\n                    {currentRound !== \"4\" && (\r\n                        <input\r\n                            min={0}\r\n                            value={inGameQuestionIndex}\r\n                            onChange={e => {\r\n                                const val = e.target.value;\r\n                                if (val === \"\") {\r\n                                    setInGameQuestionIndex(0);\r\n                                } else {\r\n                                    setInGameQuestionIndex(Number(val));\r\n                                }\r\n                            }}\r\n                            className=\"w-16 px-2 py-2 rounded-lg border border-blue-400 bg-slate-700 text-white text-center font-bold focus:outline-none focus:ring-2 focus:ring-blue-400\"\r\n                            style={{ minWidth: 0 }}\r\n                        />\r\n                    )}\r\n                    {currentRound !== \"4\" && (\r\n                        <button\r\n                        onClick={async () => {\r\n                            try {\r\n                                setCurrentQuestionIndex(inGameQuestionIndex.toString());\r\n\r\n                                // Fetch and display the specified question\r\n                                if (currentRound === \"3\") {\r\n                                    await handleNextQuestion(selectedTopic, undefined, inGameQuestionIndex.toString());\r\n                                } else if (currentRound === \"4\") {\r\n                                    await handleNextQuestion(undefined, level, inGameQuestionIndex.toString());\r\n                                } else {\r\n                                    await handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString());\r\n                                }\r\n\r\n                                toast.success(`Đã chuyển đến câu hỏi số: ${inGameQuestionIndex}`);\r\n                            } catch (error) {\r\n                                console.error(\"Error jumping to question:\", error);\r\n                                toast.error(\"Lỗi khi chuyển đến câu hỏi!\");\r\n                            }\r\n                        }}\r\n                        className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                    >\r\n                            <ArrowRightCircleIcon className=\"w-4 h-4 mr-2\" />\r\n                            CHUYỂN ĐẾN CÂU HỎI\r\n                        </button>\r\n                    )}\r\n                </div>\r\n\r\n                <div className=\"flex items-center gap-3\">\r\n                    <button\r\n                    onClick={han}\r\n                    className=\"w-full flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base\"\r\n                >\r\n                    <PlayCircleIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    BẮT ĐẦU VÒNG THI\r\n                </button>\r\n                    {/* Current Question Index Input */}\r\n                    \r\n                </div>\r\n                <button\r\n                        onClick={() => {\r\n                            if (currentRound === \"3\") {\r\n                                // Use the input box value for round 3\r\n                                handleNextQuestion(selectedTopic, undefined, \"0\")\r\n                                setInGameQuestionIndex((prev: number) => prev + 1);\r\n                                return\r\n                            }\r\n                            if (currentRound === \"4\") {\r\n                                if (level === \"Dễ\") {\r\n                                    handleNextQuestion(undefined, level, easyQuestionNumber.toString())\r\n                                    setEasyQuestionNumber((prev: number) => (prev + 1))\r\n                                }\r\n                                if (level === \"Trung bình\") {\r\n                                    handleNextQuestion(undefined, level, (20 + mediumQuestionNumber).toString())\r\n                                    setMediumQuestionNumber((prev: number) => (prev + 1))\r\n                                }\r\n                                if (level === \"Khó\") {\r\n                                    handleNextQuestion(undefined, level, (40 + hardQuestionNumber).toString())\r\n                                    setHardQuestionNumber((prev: number) => (prev + 1))\r\n                                }\r\n                                return\r\n                            }\r\n                            // Use the input box value for other rounds\r\n                            handleNextQuestion(undefined, undefined, inGameQuestionIndex.toString())\r\n                            setInGameQuestionIndex((prev: number) => prev + 1);\r\n                        }}\r\n                        className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                    >\r\n                        <ArrowRightCircleIcon className=\"w-4 h-4 mr-2\" />\r\n                        CÂU HỎI TIẾP THEO\r\n                    </button>\r\n                    <button\r\n                    onClick={() => {\r\n                        handleStartTime()\r\n                        toast.success(\"Đã bắt đầu đếm giờ!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <ClockIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    BẮT ĐẦU ĐẾM GIỜ\r\n                </button>\r\n                <button\r\n                    onClick={() => {\r\n                        handleShowAnswer()\r\n                        toast.success(\"Đã hiển thị câu trả lời cho người chơi!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <EyeIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    HIỆN CÂU TRẢ LỜI THÍ SINH\r\n                </button>\r\n               \r\n            </div>\r\n\r\n            {/* Show Answer and Start Timer - Second row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4 mb-4\">\r\n                 <button\r\n                    onClick={() => handleCorrectAnswer(currentAnswer)}\r\n                    className=\"flex items-center bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-green-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <CheckCircleIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    HIỆN ĐÁP ÁN ĐÚNG\r\n                </button>\r\n               \r\n\r\n            </div>\r\n\r\n            {/* Sound controls - Fourth row */}\r\n            <div className=\"flex flex-col gap-3 lg:gap-4\">\r\n                <button\r\n                    onClick={() => {\r\n                        playSound(roomId, currentRound)\r\n                        toast.success(`Đã chạy âm thanh cho vòng thi ${currentRound}`);\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-yellow-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <SpeakerWaveIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    CHẠY ÂM THANH BẮT ĐẦU VÒNG THI\r\n                </button>\r\n                <button\r\n                    onClick={() => {\r\n                        playSound(roomId, \"opening\")\r\n                        toast.success(\"Đã chạy âm thanh mở đầu!\");\r\n                    }}\r\n                    className=\"flex items-center bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white p-2 lg:p-3 rounded-lg shadow-md border border-red-400/50 transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full\"\r\n                >\r\n                    <MusicalNoteIcon className=\"w-4 h-4 mr-2\" />\r\n\r\n                    CHẠY ÂM THANH MỞ ĐẦU\r\n                </button>\r\n                <button\r\n                    onClick={handleToggleRules}\r\n                    className={`flex items-center ${\r\n                        showingRules\r\n                            ? 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-red-400/50'\r\n                            : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 border-green-400/50'\r\n                    } text-white p-2 lg:p-3 rounded-lg shadow-md border transition-all duration-200 hover:scale-105 font-medium text-sm lg:text-base w-full`}\r\n                >\r\n                    {showingRules ? (\r\n                        <EyeSlashIcon className=\"w-4 h-4 mr-2\" />\r\n                    ) : (\r\n                        <DocumentTextIcon className=\"w-4 h-4 mr-2\" />\r\n                    )}\r\n                    {showingRules ? 'ẨN LUẬT THI' : 'HIỂN THỊ LUẬT THI'}\r\n                </button>\r\n            </div>\r\n\r\n            {/* Modals */}\r\n            <HostGuideModal\r\n                isOpen={showGuideModal}\r\n                onClose={() => setShowGuideModal(false)}\r\n                round={currentRound}\r\n            />\r\n\r\n            <PlayerColorSelector\r\n                isOpen={showColorSelector}\r\n                onClose={() => setShowColorSelector(false)}\r\n                players={playerScores}\r\n                onSaveColors={handleSavePlayerColors}\r\n                currentColors={playerColors}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default HostManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,SAAS,QAAQ,0BAA0B;AAEpD,SAASC,SAAS,QAAQ,YAAY;AAGtC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,SACIC,eAAe,EAEfC,oBAAoB,EACpBC,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,YAAY,EACZC,sBAAsB,QAEnB,2BAA2B;AAClC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,UAAU,MAAM,gCAAgC;AAEvD,SAASC,cAAc,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG9C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IACFC,kBAAkB;IAClBC,gBAAgB;IAChBC,eAAe;IACfC,gBAAgB;IAChBC,mBAAmB;IACnBC,aAAa;IACbC,YAAY;IACZC,eAAe;IACfC,oBAAoB;IACpBC,uBAAuB;IACvBC,eAAe;IACfC,YAAY;IACZC,eAAe;IACfC,mBAAmB;IACnBC;EACJ,CAAC,GAAGzC,OAAO,CAAC,CAAC;EAEb,MAAM;IAAE0C,WAAW;IAAEC,aAAa;IAAEC,kBAAkB;IAAEC,oBAAoB;IAAEC,kBAAkB;IAAEC,qBAAqB;IAAEC,uBAAuB;IAAEC,qBAAqB;IAAEC,KAAK;IAAEC;EAAc,CAAC,GAAGhD,SAAS,CAAC,CAAC;EAE7M,MAAM,CAACiD,YAAY,CAAC,GAAGnD,eAAe,CAAC,CAAC;EACxC,MAAMoD,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAE9B,MAAMoD,YAAY,GAAGF,YAAY,CAACG,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EACrD,MAAMC,QAAQ,GAAGJ,YAAY,CAACG,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG;EACpD,MAAME,MAAM,GAAGL,YAAY,CAACG,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG;EAChD,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAEjE,MAAMiE,QAAQ,GAAG1C,cAAc,CAAC,CAAC;EAEjC,MAAM;IAAC2C,UAAU;IAAEC,gBAAgB;IAAEC;EAAkB,CAAC,GAAG9C,UAAU,CAAC,CAAC;;EAEvE;EACAD,eAAe,CAAC,CAAC;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMgD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACA,MAAMH,UAAU,CAAC;QAAER,MAAM;QAAEY,KAAK,EAAEf,YAAY;QAAEgB,IAAI,EAAE5B;MAAY,CAAC,CAAC;MACpE1B,KAAK,CAACuD,OAAO,CAAC,uBAAuBjB,YAAY,EAAE,CAAC;IACxD,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxD,KAAK,CAACwD,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACJ,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,IAAIhB,YAAY,EAAE;QACd;QACA,MAAMrD,IAAI,CAACsE,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;UAAEC,OAAO,EAAEnB;QAAO,CAAC,CAAC;QACjEE,eAAe,CAAC,KAAK,CAAC;QACtB3C,KAAK,CAACuD,OAAO,CAAC,gBAAgB,CAAC;MACnC,CAAC,MAAM;QACH;QACA,MAAMlE,IAAI,CAACsE,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;UACzCC,OAAO,EAAEnB,MAAM;UACfoB,YAAY,EAAEvB;QAClB,CAAC,CAAC;QACFK,eAAe,CAAC,IAAI,CAAC;QACrB3C,KAAK,CAACuD,OAAO,CAAC,6BAA6BjB,YAAY,EAAE,CAAC;MAC9D;IACJ,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxD,KAAK,CAACwD,KAAK,CAAC,oCAAoC,CAAC;IACrD;EACJ,CAAC;EAED,MAAMM,sBAAsB,GAAIC,MAA8B,IAAK;IAC/DxC,eAAe,CAACwC,MAAM,CAAC;IACvB/D,KAAK,CAACuD,OAAO,CAAC,0BAA0B,CAAC;EAC7C,CAAC;EAEDzE,SAAS,CAAC,MAAM;IACZ2C,sBAAsB,CAAC,CAAC,CAAC;IACzB;IACAkB,eAAe,CAAC,KAAK,CAAC;IACtB;IACAtD,IAAI,CAACsE,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;MAAEC,OAAO,EAAEnB;IAAO,CAAC,CAAC,CAACuB,KAAK,CAACP,OAAO,CAACD,KAAK,CAAC;EACpF,CAAC,EAAE,CAAClB,YAAY,CAAC,CAAC;EAElB,oBACI9B,OAAA;IAAKyD,SAAS,EAAC,kGAAkG;IAAAC,QAAA,gBAG7G1D,OAAA,CAACP,mBAAmB;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGvB9D,OAAA;MAAKyD,SAAS,EAAC,kGAAkG;MAAAC,QAAA,gBAC7G1D,OAAA;QAAKyD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxC1D,OAAA;UACI+D,OAAO,EAAEA,CAAA,KAAM1B,iBAAiB,CAAC,IAAI,CAAE;UACvCoB,SAAS,EAAC,kNAAkN;UAC5NO,KAAK,EAAC,+BAAgB;UAAAN,QAAA,gBAEtB1D,OAAA,CAACT,sBAAsB;YAACkE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEvD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GAAC,UAC9B,EAAC5B,YAAY,EAAC,KAAG,EAACA,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGA,YAAY,KAAK,GAAG,GAAG,WAAW,GAAGA,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGA,YAAY,KAAK,GAAG,GAAG,YAAY,GAAG,WAAW;MAAA;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAU9C1D,OAAA;QAAKyD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GAEnC5B,YAAY,KAAK,GAAG,iBACjB9B,OAAA;UACIiE,GAAG,EAAE,CAAE;UACPC,KAAK,EAAElD,mBAAoB;UAC3BmD,QAAQ,EAAEC,CAAC,IAAI;YACX,MAAMC,GAAG,GAAGD,CAAC,CAACE,MAAM,CAACJ,KAAK;YAC1B,IAAIG,GAAG,KAAK,EAAE,EAAE;cACZpD,sBAAsB,CAAC,CAAC,CAAC;YAC7B,CAAC,MAAM;cACHA,sBAAsB,CAACsD,MAAM,CAACF,GAAG,CAAC,CAAC;YACvC;UACJ,CAAE;UACFZ,SAAS,EAAC,oJAAoJ;UAC9Je,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAE;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACJ,EACAhC,YAAY,KAAK,GAAG,iBACjB9B,OAAA;UACA+D,OAAO,EAAE,MAAAA,CAAA,KAAY;YACjB,IAAI;cACAnD,uBAAuB,CAACI,mBAAmB,CAAC0D,QAAQ,CAAC,CAAC,CAAC;;cAEvD;cACA,IAAI5C,YAAY,KAAK,GAAG,EAAE;gBACtB,MAAM3B,kBAAkB,CAACgB,aAAa,EAAEwD,SAAS,EAAE3D,mBAAmB,CAAC0D,QAAQ,CAAC,CAAC,CAAC;cACtF,CAAC,MAAM,IAAI5C,YAAY,KAAK,GAAG,EAAE;gBAC7B,MAAM3B,kBAAkB,CAACwE,SAAS,EAAEjD,KAAK,EAAEV,mBAAmB,CAAC0D,QAAQ,CAAC,CAAC,CAAC;cAC9E,CAAC,MAAM;gBACH,MAAMvE,kBAAkB,CAACwE,SAAS,EAAEA,SAAS,EAAE3D,mBAAmB,CAAC0D,QAAQ,CAAC,CAAC,CAAC;cAClF;cAEAlF,KAAK,CAACuD,OAAO,CAAC,6BAA6B/B,mBAAmB,EAAE,CAAC;YACrE,CAAC,CAAC,OAAOgC,KAAK,EAAE;cACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;cAClDxD,KAAK,CAACwD,KAAK,CAAC,6BAA6B,CAAC;YAC9C;UACJ,CAAE;UACFS,SAAS,EAAC,uQAAuQ;UAAAC,QAAA,gBAE7Q1D,OAAA,CAACjB,oBAAoB;YAAC0E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6CAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACpC1D,OAAA;UACA+D,OAAO,EAAEa,GAAI;UACbnB,SAAS,EAAC,kQAAkQ;UAAAC,QAAA,gBAE5Q1D,OAAA,CAACd,cAAc;YAACuE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sCAG/C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGJ,CAAC,eACN9D,OAAA;QACQ+D,OAAO,EAAEA,CAAA,KAAM;UACX,IAAIjC,YAAY,KAAK,GAAG,EAAE;YACtB;YACA3B,kBAAkB,CAACgB,aAAa,EAAEwD,SAAS,EAAE,GAAG,CAAC;YACjD1D,sBAAsB,CAAE4D,IAAY,IAAKA,IAAI,GAAG,CAAC,CAAC;YAClD;UACJ;UACA,IAAI/C,YAAY,KAAK,GAAG,EAAE;YACtB,IAAIJ,KAAK,KAAK,IAAI,EAAE;cAChBvB,kBAAkB,CAACwE,SAAS,EAAEjD,KAAK,EAAEN,kBAAkB,CAACsD,QAAQ,CAAC,CAAC,CAAC;cACnEnD,qBAAqB,CAAEsD,IAAY,IAAMA,IAAI,GAAG,CAAE,CAAC;YACvD;YACA,IAAInD,KAAK,KAAK,YAAY,EAAE;cACxBvB,kBAAkB,CAACwE,SAAS,EAAEjD,KAAK,EAAE,CAAC,EAAE,GAAGL,oBAAoB,EAAEqD,QAAQ,CAAC,CAAC,CAAC;cAC5ElD,uBAAuB,CAAEqD,IAAY,IAAMA,IAAI,GAAG,CAAE,CAAC;YACzD;YACA,IAAInD,KAAK,KAAK,KAAK,EAAE;cACjBvB,kBAAkB,CAACwE,SAAS,EAAEjD,KAAK,EAAE,CAAC,EAAE,GAAGJ,kBAAkB,EAAEoD,QAAQ,CAAC,CAAC,CAAC;cAC1EjD,qBAAqB,CAAEoD,IAAY,IAAMA,IAAI,GAAG,CAAE,CAAC;YACvD;YACA;UACJ;UACA;UACA1E,kBAAkB,CAACwE,SAAS,EAAEA,SAAS,EAAE3D,mBAAmB,CAAC0D,QAAQ,CAAC,CAAC,CAAC;UACxEzD,sBAAsB,CAAE4D,IAAY,IAAKA,IAAI,GAAG,CAAC,CAAC;QACtD,CAAE;QACFpB,SAAS,EAAC,2PAA2P;QAAAC,QAAA,gBAErQ1D,OAAA,CAACjB,oBAAoB;UAAC0E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA;QACA+D,OAAO,EAAEA,CAAA,KAAM;UACX1D,eAAe,CAAC,CAAC;UACjBb,KAAK,CAACuD,OAAO,CAAC,qBAAqB,CAAC;QACxC,CAAE;QACFU,SAAS,EAAC,oQAAoQ;QAAAC,QAAA,gBAE9Q1D,OAAA,CAACf,SAAS;UAACwE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iDAG1C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA;QACI+D,OAAO,EAAEA,CAAA,KAAM;UACX3D,gBAAgB,CAAC,CAAC;UAClBZ,KAAK,CAACuD,OAAO,CAAC,yCAAyC,CAAC;QAC5D,CAAE;QACFU,SAAS,EAAC,kQAAkQ;QAAAC,QAAA,gBAE5Q1D,OAAA,CAAChB,OAAO;UAACyE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kDAGxC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAER,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAC7C1D,OAAA;QACG+D,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAACC,aAAa,CAAE;QAClDiD,SAAS,EAAC,0PAA0P;QAAAC,QAAA,gBAEpQ1D,OAAA,CAAClB,eAAe;UAAC2E,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4CAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGR,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBACzC1D,OAAA;QACI+D,OAAO,EAAEA,CAAA,KAAM;UACXnF,SAAS,CAACqD,MAAM,EAAEH,YAAY,CAAC;UAC/BtC,KAAK,CAACuD,OAAO,CAAC,iCAAiCjB,YAAY,EAAE,CAAC;QAClE,CAAE;QACF2B,SAAS,EAAC,uQAAuQ;QAAAC,QAAA,gBAEjR1D,OAAA,CAACb,eAAe;UAACsE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4DAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA;QACI+D,OAAO,EAAEA,CAAA,KAAM;UACXnF,SAAS,CAACqD,MAAM,EAAE,SAAS,CAAC;UAC5BzC,KAAK,CAACuD,OAAO,CAAC,0BAA0B,CAAC;QAC7C,CAAE;QACFU,SAAS,EAAC,gQAAgQ;QAAAC,QAAA,gBAE1Q1D,OAAA,CAACZ,eAAe;UAACqE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+CAGhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA;QACI+D,OAAO,EAAEb,iBAAkB;QAC3BO,SAAS,EAAE,qBACPvB,YAAY,GACN,gGAAgG,GAChG,kGAAkG,wIAC6B;QAAAwB,QAAA,GAExIxB,YAAY,gBACTlC,OAAA,CAACV,YAAY;UAACmE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzC9D,OAAA,CAACX,gBAAgB;UAACoE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC/C,EACA5B,YAAY,GAAG,aAAa,GAAG,mBAAmB;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGN9D,OAAA,CAACN,cAAc;MACXoF,MAAM,EAAE1C,cAAe;MACvB2C,OAAO,EAAEA,CAAA,KAAM1C,iBAAiB,CAAC,KAAK,CAAE;MACxCQ,KAAK,EAAEf;IAAa;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eAEF9D,OAAA,CAACL,mBAAmB;MAChBmF,MAAM,EAAExC,iBAAkB;MAC1ByC,OAAO,EAAEA,CAAA,KAAMxC,oBAAoB,CAAC,KAAK,CAAE;MAC3CyC,OAAO,EAAEvE,YAAa;MACtBwE,YAAY,EAAE3B,sBAAuB;MACrC4B,aAAa,EAAEpE;IAAa;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAC5D,EAAA,CAtUID,cAAc;EAAA,QAiBZzB,OAAO,EAEuLG,SAAS,EAEpLF,eAAe,EACrBC,WAAW,EASXoB,cAAc,EAE4BD,UAAU,EAGrED,eAAe;AAAA;AAAAuF,EAAA,GApCblF,cAAc;AAwUpB,eAAeA,cAAc;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}