import { ref, onValue, Unsubscribe, onDisconnect, set, get, remove, serverTimestamp } from "firebase/database";
import { database } from "../firebase-config";
import { Answer, Score, User } from "../type";
import { PlayerData } from "../shared/types";

export class FirebaseRoomListener {
  private static instances: Map<string, FirebaseRoomListener> = new Map();
  private roomId: string;

  constructor(roomId: string) {
    this.roomId = roomId;
  }

  static getInstance(roomId: string): FirebaseRoomListener {
    if (!this.instances.has(roomId)) {
      this.instances.set(roomId, new FirebaseRoomListener(roomId));
    }
    return this.instances.get(roomId)!;
  }

  private listen<T = any>(child: string, callback: (data: T) => void, skipFirst = false): Unsubscribe {
    const fullRef = ref(database, `rooms/${this.roomId}/${child}`);
    let isFirst = true;
    const unsubscribe: Unsubscribe = onValue(fullRef, (snapshot) => {
      if (skipFirst && isFirst) {
        isFirst = false;
        return;
      }
      if (!snapshot.exists()) return;
      const data: T = snapshot.val();
      callback(data);
    });
    return unsubscribe;
  }

  listenToPlayers(callback: (data: User[]) => void) {
    return this.listen("players", callback);
  }

  listenToQuestion(callback: (data: any) => void) {
    return this.listen("question", callback);
  }

  listenToSelectedCell(callback: (data: any) => void) {
    return this.listen("cell", callback);
  }

  listenToCellColor(callback: (data: any) => void) {
    return this.listen("color", callback);
  }

  listenToCurrentQuestionsNumber(callback: (data: number) => void) {
    return this.listen("currentQuestions", callback);
  }

  listenToBuzzing(callback: (data: string) => void) {
    return this.listen("buzzedPlayer", callback);
  }

  listenToStar(callback: (data: string) => void) {
    return this.listen("star", callback);
  }

  listenToObstacle(callback: (data: any) => void) {
    return this.listen("obstacles", callback);
  }

  listenToPackets(callback: (data: string[]) => void) {
    return this.listen("packets", callback);
  }

  listenToSelectedPacket(callback: (data: string) => void) {
    return this.listen("selectedPacket", callback);
  }

  listenToCurrentTurn(callback: (data: number) => void) {
    return this.listen("turn", callback);
  }

  listenToCorrectAnswer(callback: (data: string) => void) {
    return this.listen("answers", callback);
  }

  listenToSound(callback: (data: string) => void) {
    return this.listen("sound", callback, true);
  }

  listenToOpenBuzz(callback: (data: string) => void) {
    return this.listen("openBuzzed", callback);
  }

  listenToTimeStart(callback?: () => void): Unsubscribe {
    const refPath = ref(database, `rooms/${this.roomId}/times`);
    let isFirstCall = true;
    let lastStartTime = Number(localStorage.getItem("lastStartTime")) || 0;

    const unsubscribe: Unsubscribe = onValue(refPath, (snapshot) => {
      const time = snapshot.val();
      if (isFirstCall) {
        isFirstCall = false;
        return;
      }
      if (time && time !== lastStartTime) {
        lastStartTime = time;
        localStorage.setItem("lastStartTime", time.toString());
        if (callback) {
          callback();
        }
      }
    });
    return unsubscribe;
  }

  listenToScores(callback: (data: Partial<PlayerData[]>) => void) {
    return this.listen("scores", callback);
  }

  listenToHistory(callback: (data: any) => void) {
    return this.listen("round_scores", callback);
  }

  listenToGrid(callback: (data: string[][]) => void) {
    return this.listen("grid", callback);
  }

  listenToRoundStart(callback: (data: any) => void) {
    return this.listen("rounds", callback);
  }

  listenToSelectRow(callback: (data: any) => void) {
    return this.listen("select", callback);
  }

  listenToIncorrectRow(callback: (data: any) => void) {
    return this.listen("incorrect", callback);
  }

  listenToCorrectRow(callback: (data: any) => void) {
    return this.listen("correct", callback);
  }

  listenToBroadcastedAnswer(callback: (data: Partial<PlayerData[]>) => void) {
    return this.listen("answerLists", callback);
  }

  listenToSpectatorJoin(callback: (count: number) => void): Unsubscribe {
    const path = ref(database, `rooms/${this.roomId}/spectators`);
    return onValue(path, (snapshot) => {
      callback(snapshot.size);
    });
  }

  listenToRules(callback: (data: any) => void) {
    return this.listen("rules", callback);
  }

  listenToRoundRules(callback: (data: any) => void) {
    return this.listen("showRules", callback);
  }

  listenToGridActions(callback: (data: any) => void) {
    return this.listen("rounds", callback);
  }

  listenToUsedTopics(callback: (topics: string[]) => void) {
    return this.listen("usedTopics", callback);
  }

  listenToReturnToTopicSelection(callback: (shouldReturn: boolean) => void) {
    return this.listen("returnToTopicSelection", callback);
  }

  async setUsedTopic(topic: string): Promise<void> {
    const usedRef = ref(database, `rooms/${this.roomId}/usedTopics`);
    const snap = await get(usedRef);
    const current = snap.val() || [];
    if (!current.includes(topic)) {
      await set(usedRef, [...current, topic]);
    }
  }

  async setReturnToTopicSelection(value: boolean) {
    const refPath = ref(database, `rooms/${this.roomId}/returnToTopicSelection`);
    await set(refPath, value);
  }

  setupOnDisconnect = (
    roomId: string,
    userId: string,
    userData: any,
    onDisconnectCallback?: () => void
  ) => {
    const userRef = ref(database, `rooms/${roomId}/players/${userId}`);
    const disconnectHandler = onDisconnect(userRef);

    // Remove the user from players when disconnect happens
    disconnectHandler
      .remove()
      .then(() => {
        console.log(`onDisconnect handler set for user ${userId} in room ${roomId}`);
        if (onDisconnectCallback) onDisconnectCallback();
      })
      .catch((error) => {
        console.error("Failed to set onDisconnect handler:", error);
      });

    // Start heartbeat to keep user online and update lastActive
    const interval = setInterval(() => {
      set(userRef, { ...userData, lastActive: serverTimestamp() });
    }, 5000);

    // Cleanup: cancel onDisconnect and clear heartbeat
    return () => {
      clearInterval(interval);
      disconnectHandler
        .cancel()
        .then(() => console.log(`onDisconnect handler canceled for user ${userId}`))
        .catch((err) => console.error("Failed to cancel onDisconnect handler:", err));
    };
  };

  async deletePath(path: string): Promise<void> {
    const refPath = ref(database, `rooms/${this.roomId}/${path}`);
    await remove(refPath);
  }

  async addPlayer(uid: string, data: any) {
    const pathRef = ref(database, `rooms/${this.roomId}/players/${uid}`);
    await set(pathRef, {
      joined_at: Date.now(),
      data,
    });
  }
}
